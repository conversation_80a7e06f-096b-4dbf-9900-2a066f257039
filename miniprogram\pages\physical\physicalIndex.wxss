.knowledge-container {
  min-height: 100vh;
  background: linear-gradient(to bottom, #f0f7fa, #e5f4fb);
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(92, 83, 70, 0.15);
  margin-bottom: 30rpx;
  text-align: center;
  border-left: 8rpx solid #e8c4a1;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15rpx;
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #5c5346;
  margin-bottom: 12rpx;
}

.title-decoration {
  width: 80rpx;
  height: 6rpx;
  background-color: #e8c4a1;
  border-radius: 3rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #8a7e6e;
  margin-top: 8rpx;
}

/* 顶部tab菜单样式 */
.tab-container {
  display: flex;
  background: linear-gradient(to right, #e6f4fb, #dceffa);
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(120, 170, 200, 0.15);
  position: relative;
  overflow: hidden;
}

.tab-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5rpx);
  z-index: 0;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.tab text {
  font-size: 32rpx;
  color: #7a8c9a;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
}

.tab.active text {
  color: #3a7cad;
  font-weight: 600;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  border-radius: 4rpx;
  background: linear-gradient(to right, #70b1e0, #4e95cc);
  box-shadow: 0 2rpx 4rpx rgba(112, 177, 224, 0.3);
}

/* 管理员操作区域 */
.admin-actions {
  margin-bottom: 30rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background: linear-gradient(135deg, #70b1e0, #4e95cc);
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  box-shadow: 0 6rpx 16rpx rgba(112, 177, 224, 0.25);
  transition: all 0.3s ease;
}

.add-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(112, 177, 224, 0.2);
}

.add-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
  line-height: 30rpx;
}

/* 文章列表 */
.knowledge-list {
  width: 100%;
}

.article-card {
  display: flex;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(120, 170, 200, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.article-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(120, 170, 200, 0.1);
}

.card-left {
  width: 200rpx;
  height: 200rpx;
  margin-right: 24rpx;
  overflow: hidden;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.article-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  transition: transform 0.6s ease;
}

.article-card:active .article-image {
  transform: scale(1.05);
}

.card-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.article-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #3a7cad;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.article-summary {
  font-size: 28rpx;
  color: #7a8c9a;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.article-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.article-author {
  color: #70b1e0;
  margin-right: 16rpx;
}

.article-time {
  color: #a5b5c2;
}

/* 管理员操作按钮 */
.admin-buttons {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
}

.icon-btn {
  width: 60rpx;
  height: 60rpx;
  padding: 0;
  margin-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  font-size: 32rpx;
  line-height: 1;
}

.icon-btn::after {
  border: none;
}

.edit-btn {
  color: #70b1e0;
}

.delete-btn {
  color: #ff7e7e;
}

/* 空状态提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 26rpx;
  color: #999;
}

/* 加载更多区域 */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.loading {
  display: flex;
  align-items: center;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #70b1e0;
  margin: 0 8rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.load-more, .no-more {
  font-size: 26rpx;
  color: #7a8c9a;
}

.load-more {
  padding: 10rpx 40rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.6);
}

.no-more {
  opacity: 0.6;
}

/* 安宁疗护机构样式 */
.institution-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(120, 170, 200, 0.15);
}

.coming-soon-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.7;
}

.coming-soon-text {
  font-size: 30rpx;
  color: #7a8c9a;
  font-style: italic;
}

/* 搜索框样式 */
.search-container {
  margin-bottom: 10rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 10rpx 20rpx;
  margin: 0 10rpx;
  box-shadow: 0 3rpx 10rpx rgba(120, 170, 200, 0.1);
  border: 1rpx solid #f9e3d7; /* 温馨边框色 */
}

.search-box icon {
  margin-right: 10rpx;
  color: #e8c4a1; /* 温馨图标色 */
  font-size: 16px;
}

.clear-icon {
  margin-left: 10rpx;
  margin-right: 0;
}

.search-box input {
  flex: 1;
  height: 50rpx;
  font-size: 26rpx;
  color: #795548;
}

/* 城市选择区域样式 */
.location-container {
  display: flex;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(120, 170, 200, 0.15);
  height: calc(100vh - 350rpx);
  margin-top: 10rpx;
}

/* 左侧省份列表 */
.province-list {
  width: 140rpx;
  height: 100%;
  background-color: #f5f5f5;
  border-right: 1px solid #ebebeb;
}

.province-item {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.province-item.selected {
  background-color: #ffffff;
  color: #4a9cf0;
  font-weight: bold;
}

.province-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background-color: #4a9cf0;
  border-radius: 0 3rpx 3rpx 0;
}

/* 右侧城市和机构区域 */
.city-district-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  overflow: hidden;
}

/* 当前选中的省份 */
.selected-province {
  padding: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

/* 城市横向滚动 */
.city-scroll {
  height: 80rpx;
  white-space: nowrap;
  padding: 0rpx 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.city-scroll-container {
  display: inline-flex;
  padding: 0 10rpx;
  align-items: center;
  height: 100%;
}

.no-city-tip {
  display: inline-block;
  line-height: 60rpx;
  padding: 0 20rpx;
  color: #999;
  font-size: 28rpx;
}

.city-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 30rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 30rpx;
}

.city-item.selected {
  background-color: #4a9cf0;
  color: #ffffff;
}

/* 安宁疗护机构列表 */
.institution-scroll {
  height: calc(100vh - 400rpx);
}

.institution-list {
  width: 96%;
  padding: 10rpx 2%;
}

.institution-card {
  display: flex;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(120, 170, 200, 0.15);
  transition: all 0.3s ease;
  position: relative;
  height: 140rpx; /* 减小高度 */
  border-left: 6rpx solid #f3bea3; /* 添加温馨色调边框 */
  overflow: hidden;
  max-width: 96%;
}

.institution-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(120, 170, 200, 0.1);
}

.institution-card .card-left {
  width: 120rpx; /* 减小图标尺寸 */
  height: 120rpx; /* 减小图标尺寸 */
  margin-right: 20rpx;
}

.institution-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  background-color: #f9e3d7; /* 温馨背景色 */
  padding: 10rpx;
  box-sizing: border-box;
}

.institution-card .card-right {
  padding: 5rpx 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.institution-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #795548; /* 温馨棕色调 */
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.institution-address {
  font-size: 24rpx;
  color: #9e9e9e;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 管理员操作按钮 */
.admin-institution-actions {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  display: flex;
  z-index: 10;
}

.institution-action-btn {
  width: 30rpx;
  height: 30rpx;
  padding: 0;
  margin-left: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  font-size: 16rpx;
  line-height: 1;
  color: #e8846e;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
  min-width: 30rpx; /* 设置最小宽度 */
  max-width: 30rpx; /* 设置最大宽度 */
}

.institution-action-btn::after {
  border: none;
}

/* 添加机构按钮 */
.add-institution-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #f5a890, #e8846e);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
  box-shadow: 0 6rpx 16rpx rgba(232, 132, 110, 0.3);
  z-index: 10;
}

.add-institution-btn:active {
  transform: scale(0.95);
} 