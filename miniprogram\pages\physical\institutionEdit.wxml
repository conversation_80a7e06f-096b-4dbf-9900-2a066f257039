<view class="container">
  <view class="header">
    <view class="title">{{id ? '编辑机构信息' : '添加机构信息'}}</view>
    <view class="subtitle">请填写机构的详细信息</view>
  </view>
  
  <!-- 机构信息表单 -->
  <view class="form-container">
    <!-- 封面图片 -->
    <view class="form-image-item">
      <view class="form-label">机构图片</view>
      <view class="image-container" bindtap="chooseImage">
        <image class="institution-image" src="{{image}}" mode="aspectFill"></image>
        <view class="image-mask" wx:if="{{isUploading}}">
          <view class="loading-spinner"></view>
        </view>
        <view class="image-tip">点击更换</view>
      </view>
    </view>
    
    <!-- 机构名称 -->
    <view class="form-item">
      <view class="form-label">机构名称 <text class="required">*</text></view>
      <input class="form-input" type="text" value="{{name}}" placeholder="请输入机构名称" data-field="name" bindinput="inputChange" />
    </view>
    
    <!-- 所在地区 -->
    <view class="form-item">
      <view class="form-label">所在地区 <text class="required">*</text></view>
      <view class="location-picker">
        <!-- 省份 -->
        <view class="location-item" bindtap="showProvinceSelector">
          <text class="location-text">{{province || '请选择省'}}</text>
          <text class="location-arrow">></text>
        </view>
        
        <!-- 城市（非直辖市才显示） -->
        <view class="location-item" bindtap="showCitySelector" wx:if="{{showCityField}}">
          <text class="location-text">{{city || '请选择市'}}</text>
          <text class="location-arrow">></text>
        </view>
        
        <!-- 区县 -->
        <view class="location-item" bindtap="showDistrictSelector">
          <text class="location-text">{{district || '请选择区/县'}}</text>
          <text class="location-arrow">></text>
        </view>
      </view>
    </view>
    
    <!-- 详细地址 -->
    <view class="form-item">
      <view class="form-label">详细地址 <text class="required">*</text></view>
      <input class="form-input" type="text" value="{{address}}" placeholder="请输入详细地址" data-field="address" bindinput="inputChange" />
    </view>
    
    <!-- 联系电话 -->
    <view class="form-item">
      <view class="form-label">联系电话</view>
      <input class="form-input" type="number" value="{{phone}}" placeholder="请输入联系电话" data-field="phone" bindinput="inputChange" />
    </view>
    
    <!-- 服务对象 -->
    <view class="form-item">
      <view class="form-label">服务对象</view>
      <input class="form-input" type="text" value="{{serviceTarget}}" placeholder="请输入服务对象，如：晚期患者、老年人等" data-field="serviceTarget" bindinput="inputChange" />
    </view>
    
    <!-- 服务内容 -->
    <view class="form-item">
      <view class="form-label">服务内容</view>
      <textarea class="form-textarea" value="{{serviceItems}}" placeholder="请输入提供的服务内容" data-field="serviceItems" bindinput="inputChange"></textarea>
    </view>
  </view>
  
  <!-- 保存按钮 -->
  <view class="btn-container">
    <button class="save-btn" bindtap="saveInstitution">保存</button>
  </view>
  
  <!-- 地区选择器 -->
  <view class="picker-mask" wx:if="{{showCityPicker}}" bindtap="closeSelector"></view>
  <view class="picker-container {{showCityPicker ? 'show' : ''}}">
    <view class="picker-header">
      <text class="picker-title">请选择</text>
      <view class="picker-close" bindtap="closeSelector">×</view>
    </view>
    
    <!-- 省份列表 -->
    <scroll-view class="picker-list" scroll-y wx:if="{{showProvinceList}}">
      <view class="picker-item {{province === item ? 'active' : ''}}" 
            wx:for="{{allProvinces}}" 
            wx:key="index" 
            data-value="{{item}}" 
            bindtap="selectProvince">
        {{item}}
      </view>
    </scroll-view>
    
    <!-- 城市列表 -->
    <scroll-view class="picker-list" scroll-y wx:if="{{showCityList}}">
      <view wx:if="{{cityList.length === 0}}" class="picker-empty">暂无城市数据</view>
      <view class="picker-item {{city === item ? 'active' : ''}}" 
            wx:for="{{cityList}}" 
            wx:key="index" 
            data-value="{{item}}" 
            bindtap="selectCity">
        {{item}}
      </view>
    </scroll-view>
    
    <!-- 区县列表 -->
    <scroll-view class="picker-list" scroll-y wx:if="{{showDistrictList}}">
      <view wx:if="{{districtList.length === 0}}" class="picker-empty">暂无区县数据</view>
      <view class="picker-item {{district === item ? 'active' : ''}}" 
            wx:for="{{districtList}}" 
            wx:key="index" 
            data-value="{{item}}" 
            bindtap="selectDistrict">
        {{item}}
      </view>
    </scroll-view>
  </view>
</view> 