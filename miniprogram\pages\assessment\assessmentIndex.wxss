/* 页面容器 */
.assessment-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.admin-actions {
  display: flex;
}

.add-btn {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 6rpx;
  line-height: 1.5;
  margin: 0;
}

/* 文章列表 */
.article-list {
  margin-bottom: 20rpx;
}

.article-item {
  background-color: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.article-content {
  width: 100%;
}

.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.article-summary {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.article-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.article-time {
  color: #999;
}

.article-actions {
  display: flex;
  align-items: center;
}

.action-edit, .action-delete {
  padding: 4rpx 12rpx;
  margin-left: 20rpx;
  color: #4285f4;
}

.action-delete {
  color: #ff4d4f;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin: 20rpx 0;
}

.load-more-btn {
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 6rpx;
  width: 80%;
  line-height: 1.5;
}

.load-more-loading, .load-more-end {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  color: #999;
  font-size: 26rpx;
}

.load-more-loading text, .load-more-end text {
  margin-left: 10rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.empty-state text {
  margin-top: 20rpx;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  color: #ff4d4f;
  font-size: 28rpx;
}

.error-state text {
  margin: 20rpx 0;
}

.retry-btn {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 6rpx;
  margin-top: 20rpx;
  line-height: 1.5;
}

.knowledge-container {
  padding: 30rpx;
  background-color: #f0f7fa; /* 温暖的淡蓝背景 */
  min-height: 100vh;
}

/* 顶部标题样式 */
.header {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(65, 132, 190, 0.15); /* 蓝色调阴影 */
  margin-bottom: 30rpx;
  text-align: center;
  border-left: 8rpx solid #70b1e0; /* 蓝色左边框 */
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15rpx;
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #3a7cad; /* 蓝色标题 */
  margin-bottom: 12rpx;
}

.title-decoration {
  width: 80rpx;
  height: 6rpx;
  background-color: #70b1e0; /* 蓝色装饰线 */
  border-radius: 3rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #7a8c9a; /* 温暖的副标题颜色 */
  margin-top: 8rpx;
}

/* 管理员添加按钮样式 */
.admin-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #70b1e0; /* 蓝色按钮 */
  color: #ffffff;
  font-size: 28rpx;
  padding: 10rpx 25rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(112, 177, 224, 0.3); /* 匹配按钮的阴影 */
  line-height: 1.5;
}

.add-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

/* 文章列表样式 */
.knowledge-list {
  margin-top: 25rpx;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 220rpx;
  height: 220rpx;
  margin-bottom: 25rpx;
}

.empty-text {
  font-size: 34rpx;
  color: #7a8c9a; /* 温暖的文字颜色 */
  margin-bottom: 12rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #a5b5c2; /* 温暖的副文字颜色 */
}

.article-card {
  display: flex;
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 4rpx 10rpx rgba(65, 132, 190, 0.1); /* 蓝色调阴影 */
  position: relative;
  border-bottom: 3rpx solid #e5f4fb; /* 淡蓝色底部边框 */
}

.card-left {
  flex-shrink: 0;
  width: 180rpx;
  height: 180rpx;
  margin-right: 25rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.article-image {
  width: 100%;
  height: 100%;
}

.card-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}

.article-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #3a7cad; /* 蓝色标题 */
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-summary {
  font-size: 28rpx;
  color: #7a8c9a; /* 温暖的摘要文字颜色 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 12rpx;
}

.article-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #a5b5c2; /* 温暖的信息文字颜色 */
}

.article-author {
  max-width: 45%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 管理员操作按钮 */
.admin-buttons {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  display: flex;
}

.icon-btn {
  background: none;
  padding: 0;
  margin: 0 0 0 20rpx;
  border: none;
  outline: none;
  line-height: 1;
  font-size: 36rpx;
}

.icon-btn::after {
  display: none;
}

.edit-btn {
  color: #70b1e0;
}

.delete-btn {
  color: #ff7e7e;
}

/* 加载状态样式 */
.loading-container {
  text-align: center;
  padding: 25rpx 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #70b1e0; /* 蓝色加载点 */
  margin: 0 6rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.load-more {
  font-size: 28rpx;
  color: #70b1e0; /* 蓝色按钮文字 */
}

.no-more {
  font-size: 28rpx;
  color: #bfc9bf; /* 温暖的结束文字颜色 */
} 