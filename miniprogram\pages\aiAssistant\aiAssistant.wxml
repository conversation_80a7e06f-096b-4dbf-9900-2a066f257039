<view class="chat-container">
  <!-- 聊天记录区域 -->
  <scroll-view class="chat-list" scroll-y="true" scroll-into-view="{{scrollToMessage}}">
    <view class="message-list">
      <view wx:for="{{messages}}" 
            wx:key="index" 
            class="message-item {{item.role === 'user' ? 'user-message' : 'ai-message'}}"
            id="msg-{{index}}">
        <view class="message-content">
          <text>{{item.content}}</text>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-area">
    <input class="message-input" 
           value="{{inputValue}}" 
           bindinput="onInput" 
           placeholder="请输入您的问题"
           confirm-type="send"
           bindconfirm="sendMessage"/>
    <button class="send-btn" 
            bindtap="sendMessage" 
            disabled="{{!inputValue}}">
      {{isLoading ? '发送中...' : '发送'}}
    </button>
  </view>

  <!-- 清空按钮 -->
  <view class="clear-btn" bindtap="clearChat">
    <text>清空聊天</text>
  </view>
</view> 