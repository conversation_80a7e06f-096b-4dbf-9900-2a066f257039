/* pages/functionList/functionList.wxss */
.container {
    padding: 40rpx 30rpx;
    background: linear-gradient(to bottom, #fff8f0, #fcf1e7); /* 更温暖的背景渐变 */
    min-height: 100vh;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  
  .header {
    margin-bottom: 50rpx;
    text-align: center;
    padding: 20rpx;
  }
  
  .title {
    font-size: 28rpx;
    font-weight: bold;
    color: #6a5c4c; /* 温暖的褐色 */
    margin-bottom: 15rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #9c8e7f;
    margin-top: 10rpx;
    position: relative;
    display: inline-block;
  }
  
  .subtitle::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10rpx;
    transform: translateX(-50%);
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(to right, transparent, #e0c5ac, transparent);
    border-radius: 2rpx;
  }
  
  /* 功能列表容器 */
  .function-list-container {
    width: 100%;
    padding: 10rpx;
  }
  
  /* 每个功能项作为卡片 */
  .function-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.85); /* 半透明背景 */
    border-radius: 24rpx;
    padding: 30rpx 35rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 20rpx rgba(211, 187, 164, 0.15);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }
  
  .function-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8rpx;
    height: 100%;
    background: linear-gradient(to bottom, #ffb88c, #ffa372); /* 左侧装饰条 */
    border-radius: 4rpx 0 0 4rpx;
  }
  
  .function-card:active {
    transform: translateY(2rpx) scale(0.99);
    box-shadow: 0 4rpx 10rpx rgba(211, 187, 164, 0.1);
    background-color: rgba(255, 253, 250, 0.95);
  }
  
  /* 左侧图标和文字组合 */
  .item-left {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
  }
  
  /* 图标背景圆形 */
  .icon-background {
    width: 70rpx;
    height: 70rpx;
    flex-shrink: 0;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffb88c, #ffa372); /* 温暖的橙色渐变 */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 25rpx;
    box-shadow: 0 4rpx 8rpx rgba(255, 163, 114, 0.3);
  }
  
  /* 图标样式 */
  .item-icon {
    width: 40rpx;
    height: 40rpx;
  }
  
  /* 文字容器 */
  .item-text-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }
  
  .item-text {
    font-size: 32rpx;
    color: #6a5c4c;
    font-weight: 500;
    letter-spacing: 1rpx;
    margin-bottom: 8rpx;
  }
  
  .item-description {
    font-size: 24rpx;
    color: #9c8e7f;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* 右侧箭头图标 */
  .arrow-icon {
    width: 30rpx;
    height: 30rpx;
    opacity: 0.5;
    transition: transform 0.3s ease;
    margin-left: 20rpx;
    flex-shrink: 0;
  }
  
  .function-card:active .arrow-icon {
    transform: translateX(5rpx);
  }
  
  .no-functions {
    padding: 100rpx 40rpx;
    text-align: center;
    color: #9c8e7f;
    font-size: 28rpx;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 20rpx;
    box-shadow: 0 6rpx 16rpx rgba(211, 187, 164, 0.1);
    margin: 40rpx 0;
  }
  
  .footer-tip {
    text-align: center;
    font-size: 26rpx;
    color: #b3a090;
    margin-top: auto;
    padding: 40rpx 0 20rpx;
    font-style: italic;
  }
  /* ``` */