.institution-detail {
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
  min-height: 100vh;
}

/* 容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(to bottom, #f8f1eb, #f9e3d7);
  padding-bottom: 40rpx;
  padding-top: 0;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(232, 132, 110, 0.3);
  border-top: 6rpx solid #e8846e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 30rpx;
  color: #a1887f;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 30rpx;
}

.error-text {
  margin-top: 30rpx;
  font-size: 32rpx;
  color: #795548;
  text-align: center;
}

.back-btn {
  margin-top: 60rpx;
  width: 300rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #f5a890, #e8846e);
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 头部图片 */
.header-image-container {
  width: 100%;
  height: 400rpx;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-top: 0;
}

.header-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 管理员按钮 */
.admin-buttons {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
}

.icon-btn {
  width: 80rpx;
  height: 80rpx;
  margin-left: 20rpx;
  border-radius: 40rpx;
  background-color: rgba(255, 255, 255, 0.8);
  color: #e8846e;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.icon-btn::after {
  border: none;
}

/* 卡片共同样式 */
.common-card {
  width: calc(100% - 60rpx);
  margin-left: 30rpx;
  margin-right: 30rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #f3bea3;
  box-sizing: border-box;
}

/* 信息卡片 */
.info-card {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  padding: 40rpx 30rpx;
  min-height: 300rpx;
}

.institution-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #795548;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f3e0d6;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx dashed #f3e0d6;
  width: 100%;
}

.info-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.info-icon {
  width: 60rpx;
  height: 60rpx;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
  background-color: #f9e3d7;
  border-radius: 30rpx;
  color: #e8846e;
}

.info-content {
  flex: 1;
  width: 0;
  max-width: 100%;
}

.info-label {
  font-size: 28rpx;
  color: #a1887f;
  margin-bottom: 10rpx;
  text-align: left;
  font-weight: bold;
}

.info-value {
  font-size: 30rpx;
  color: #5d4037;
  line-height: 1.6;
  text-align: left;
  width: 100%;
  background-color: #f9f5f2;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.address-value {
  word-break: break-all;
  white-space: normal;
  width: 100%;
}

.info-arrow {
  color: #bcaaa4;
  font-size: 30rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.call-btn {
  width: 100rpx;
  height: 50rpx;
  background-color: #e8846e;
  color: #fff;
  font-size: 26rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  flex-shrink: 0;
}

/* 描述卡片 */
.description-card, .services-card {
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #795548;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f3e0d6;
  text-align: left;
}

.description-content {
  font-size: 28rpx;
  color: #5d4037;
  line-height: 1.8;
  text-align: justify;
}

.services-placeholder {
  font-size: 28rpx;
  color: #bcaaa4;
  text-align: left;
  padding: 40rpx 20rpx;
  background-color: #f9f5f2;
  border-radius: 12rpx;
}

/* 机构名称 */
.institution-title {
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.institution-title text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 机构基本信息 */
.info-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.info-label icon {
  margin-right: 10rpx;
  color: #4a9cf0;
}

.info-label text {
  font-size: 28rpx;
  color: #666;
}

.info-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
}

/* 机构介绍 */
.description-section {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  margin-bottom: 120rpx; /* 为底部按钮预留空间 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 联系按钮 */
.contact-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.contact-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  margin: 0 10rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #ffffff;
}

.phone-btn {
  background-color: #4a9cf0;
}

.map-btn {
  background-color: #3cc51f;
}

/* 服务内容卡片 */
.services-card {
  margin-bottom: 30rpx;
  padding: 40rpx 30rpx;
  min-height: 200rpx;
}

.services-content {
  font-size: 30rpx;
  color: #5d4037;
  line-height: 1.8;
  text-align: left;
  word-break: break-all;
  white-space: pre-wrap;
  width: 100%;
  background-color: #f9f5f2;
  padding: 20rpx;
  border-radius: 12rpx;
  box-sizing: border-box;
} 