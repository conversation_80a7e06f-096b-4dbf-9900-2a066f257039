const app = getApp();

Page({
  data: {
    reminders: [], // 提醒列表
    isAdding: false, // 是否正在添加提醒
    newReminder: {
      name: '', // 药品名称
      time: '', // 提醒时间
      days: [], // 重复天数
      note: '' // 备注
    },
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    selectedDays: [], // 选中的重复天数
    isSubscribed: false // 是否已订阅消息
  },

  onLoad: function() {
    // 检查是否已订阅消息
    this.checkSubscriptionStatus();
    // 加载提醒列表
    this.loadReminders();
  },

  // 检查订阅状态
  checkSubscriptionStatus: function() {
    const db = wx.cloud.database();
    const openid = wx.getStorageSync('openid');
    
    if (!openid) return;
    
    db.collection('subscriptions')
      .where({
        _openid: openid,
        type: 'medication'
      })
      .get()
      .then(res => {
        this.setData({
          isSubscribed: res.data.length > 0
        });
      });
  },

  // 加载提醒列表
  loadReminders: function() {
    const db = wx.cloud.database();
    const openid = wx.getStorageSync('openid');
    
    if (!openid) return;
    
    db.collection('medicationReminders')
      .where({
        _openid: openid
      })
      .orderBy('time', 'asc')
      .get()
      .then(res => {
        this.setData({
          reminders: res.data
        });
      });
  },

  // 添加新提醒
  addReminder: function() {
    this.setData({
      isAdding: true,
      newReminder: {
        name: '',
        time: '',
        days: [],
        note: ''
      },
      selectedDays: []
    });
  },

  // 取消添加
  cancelAdd: function() {
    this.setData({
      isAdding: false
    });
  },

  // 输入药品名称
  onNameInput: function(e) {
    this.setData({
      'newReminder.name': e.detail.value
    });
  },

  // 选择时间
  onTimeChange: function(e) {
    this.setData({
      'newReminder.time': e.detail.value
    });
  },

  // 选择重复天数
  onDaySelect: function(e) {
    const day = parseInt(e.currentTarget.dataset.day);
    const selectedDays = [...this.data.selectedDays];
    const index = selectedDays.indexOf(day);
    
    if (index > -1) {
      selectedDays.splice(index, 1);
    } else {
      selectedDays.push(day);
    }
    
    // 对选中的天数进行排序
    selectedDays.sort((a, b) => a - b);
    
    this.setData({
      selectedDays,
      'newReminder.days': selectedDays
    });
  },

  // 输入备注
  onNoteInput: function(e) {
    this.setData({
      'newReminder.note': e.detail.value
    });
  },

  // 保存提醒
  saveReminder: function() {
    if (!this.data.newReminder.name || !this.data.newReminder.time) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    const db = wx.cloud.database();
    const openid = wx.getStorageSync('openid');
    
    if (!openid) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 检查是否已订阅消息
    if (!this.data.isSubscribed) {
      this.requestSubscribeMessage();
      return;
    }

    db.collection('medicationReminders').add({
      data: {
        ...this.data.newReminder,
        _openid: openid,
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    }).then(() => {
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });
      this.setData({
        isAdding: false
      });
      this.loadReminders();
    });
  },

  // 请求订阅消息
  requestSubscribeMessage: function() {
    // 这里需要替换为您的实际模板ID
    const templateId = '';
    
    wx.requestSubscribeMessage({
      tmplIds: [templateId],
      success: (res) => {
        if (res[templateId] === 'accept') {
          // 用户同意订阅
          const db = wx.cloud.database();
          const openid = wx.getStorageSync('openid');
          
          db.collection('subscriptions').add({
            data: {
              _openid: openid,
              type: 'medication',
              createTime: db.serverDate()
            }
          }).then(() => {
            this.setData({
              isSubscribed: true
            });
            // 继续保存提醒
            this.saveReminder();
          });
        } else {
          wx.showToast({
            title: '需要订阅消息才能设置提醒',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('订阅消息失败:', err);
        wx.showToast({
          title: '订阅消息失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除提醒
  deleteReminder: function(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个提醒吗？',
      success: (res) => {
        if (res.confirm) {
          const db = wx.cloud.database();
          
          db.collection('medicationReminders')
            .doc(id)
            .remove()
            .then(() => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.loadReminders();
            });
        }
      }
    });
  },

  // 编辑提醒
  editReminder: function(e) {
    const id = e.currentTarget.dataset.id;
    const reminder = this.data.reminders.find(r => r._id === id);
    
    if (reminder) {
      this.setData({
        isAdding: true,
        newReminder: {
          ...reminder,
          _id: id
        },
        selectedDays: reminder.days || []
      });
    }
  }
}); 