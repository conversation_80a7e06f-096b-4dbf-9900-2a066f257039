const app = getApp();

Page({
  data: {
    messages: [], // 聊天记录
    inputValue: '', // 输入框的值
    isLoading: false, // 是否正在加载
    botId: '', // 将会从列表中获取正确的botId
    history: [], // 历史对话
    scrollToMessage: '' // 用于自动滚动
  },

  onLoad: async function() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }
    
    try {
      // 先获取机器人列表
      const botList = await wx.cloud.extend.AI.bot.list({
        data: {}
      });
      
      console.log('可用机器人列表:', botList);
      
      if (botList && botList.data && botList.data.length > 0) {
        // 使用列表中的第一个机器人
        const botId = botList.data[0].botId;
        console.log('使用的机器人ID:', botId);
        
        this.setData({ botId: botId });
        
        // 获取机器人配置信息
        const res = await wx.cloud.extend.AI.bot.get({
          data: {
            botId: botId
          }
        });
        
        console.log('机器人配置信息:', res);
        
        // 初始化欢迎消息，使用云端配置的招呼语
        this.setData({
          messages: [{
            role: 'assistant',
            content: res.introduction || '您好！我是您的AI助手，请问有什么可以帮您？'
          }]
        });
      } else {
        console.error('没有可用的机器人');
        this.setData({
          messages: [{
            role: 'assistant',
            content: '您好！我是您的AI助手，请问有什么可以帮您？'
          }]
        });
      }
    } catch (err) {
      console.error('初始化机器人失败:', err);
      // 如果获取失败，使用默认欢迎语
      this.setData({
        messages: [{
          role: 'assistant',
          content: '您好！我是您的AI助手，请问有什么可以帮您？'
        }]
      });
    }
  },

  // 输入框内容变化
  onInput: function(e) {
    const value = e.detail.value;
    console.log('输入内容:', value); // 添加日志
    this.setData({
      inputValue: value
    });
  },

  // 发送消息
  sendMessage: async function() {
    const content = this.data.inputValue.trim();
    console.log('准备发送消息:', content); // 添加日志
    
    if (!content) {
      console.log('消息内容为空，不发送'); // 添加日志
      return;
    }
    
    if (this.data.isLoading) {
      console.log('正在发送中，请等待'); // 添加日志
      return;
    }
    
    if (!this.data.botId) {
      console.error('机器人ID未设置');
      wx.showToast({
        title: '系统配置错误，请联系管理员',
        icon: 'none'
      });
      return;
    }
    
    const userMessage = {
      role: 'user',
      content: content
    };

    // 添加到消息列表
    this.setData({
      messages: [...this.data.messages, userMessage],
      inputValue: '',
      isLoading: true,
      scrollToMessage: `msg-${this.data.messages.length}`
    });

    try {
      console.log('调用AI接口，参数:', { // 添加日志
        botId: this.data.botId,
        msg: content,
        history: this.data.history
      });
      
      // 使用 AI.bot.sendMessage 替代直接调用模型
      const res = await wx.cloud.extend.AI.bot.sendMessage({
        data: {
          botId: this.data.botId,
          msg: content,
          history: this.data.history
        },
        onText: (text) => {
          console.log('收到AI回复:', text); // 添加日志
          // 收到AI回复的文本
          const aiMessage = {
            role: 'assistant',
            content: text
          };
          this.setData({
            messages: [...this.data.messages, aiMessage],
            scrollToMessage: `msg-${this.data.messages.length}`
          });
        },
        onFinish: (text) => {
          console.log('AI回复完成'); // 添加日志
          // 对话结束
          this.setData({
            isLoading: false,
            history: [
              ...this.data.history,
              userMessage,
              {
                role: 'assistant',
                content: text
              }
            ]
          });
        }
      });
    } catch (err) {
      console.error('AI回复错误:', err);
      wx.showToast({
        title: '抱歉，AI助手暂时无法回复',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
    }
  },

  // 清空聊天记录
  clearChat: function() {
    wx.showModal({
      title: '提示',
      content: '确定要清空聊天记录吗？',
      success: (res) => {
        if (res.confirm) {
          if (this.data.botId) {
            // 重新获取机器人配置信息
            wx.cloud.extend.AI.bot.get({
              data: {
                botId: this.data.botId
              }
            }).then(res => {
              this.setData({
                messages: [{
                  role: 'assistant',
                  content: res.introduction || '您好！我是您的AI助手，请问有什么可以帮您？'
                }],
                history: []
              });
            }).catch(err => {
              console.error('获取机器人配置失败:', err);
              this.setData({
                messages: [{
                  role: 'assistant',
                  content: '您好！我是您的AI助手，请问有什么可以帮您？'
                }],
                history: []
              });
            });
          } else {
            this.setData({
              messages: [{
                role: 'assistant',
                content: '您好！我是您的AI助手，请问有什么可以帮您？'
              }],
              history: []
            });
          }
        }
      }
    });
  }
}); 