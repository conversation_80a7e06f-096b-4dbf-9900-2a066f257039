<view class="page-container">

 <view class="search-bar">
  <icon type="search" size="16" color="#999" class="search-icon"></icon>
  <input class="search-input" placeholder="搜索信息..." placeholder-class="search-placeholder" confirm-type="search" bindconfirm="onSearchConfirm" value="{{searchKeyword}}" />
  <!-- 搜索取消按钮 -->
  <view class="search-cancel" bindtap="resetSearch" wx:if="{{searchKeyword}}">取消</view>
  </view>

<swiper class="carousel" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}">
  <block wx:for="{{carouselItems}}" wx:key="id">
    <swiper-item>
      <image src="{{item.imageUrl}}" class="carousel-image" mode="aspectFill" />
      <view class="carousel-edit-btn" wx:if="{{isAdmin}}" catchtap="editCarousel" data-id="{{item.id}}">
        <text class="edit-icon">📷</text>
      </view>
    </swiper-item>
  </block>
</swiper>

<view class="role-modules">
  <view class="role-item" wx:for="{{roleModules}}" wx:key="id" bindtap="navigateToRole" data-role="{{item.roleType}}">
    <image class="role-icon" src="{{item.iconUrl}}" mode="aspectFit"></image>
    <text class="role-name">{{item.name}}</text>
  </view>
</view>

<view class="article-list-title">
  <block wx:if="{{searchKeyword}}">搜索结果: "{{searchKeyword}}"</block>
  <block wx:else>最新资讯</block>
</view>

<view class="article-list">
  <block wx:if="{{articles.length > 0}}">
    <view class="article-item" wx:for="{{articles}}" wx:key="_id" bindtap="navigateToDetail" data-id="{{item._id}}">
      <image class="article-image" src="{{item.coverImage || '/images/newspaper.png'}}" mode="aspectFill"></image>
      <view class="article-content">
        <text class="article-title">{{item.title}}</text>
        <text class="article-brief">{{item.brief}}</text>
        <view class="article-info">
          <text class="article-date">{{item.createTime}}</text>
          <text class="article-author" wx:if="{{item.author}}">{{item.author}}</text>
        </view>
      </view>
    </view>
  </block>
  <block wx:else>
    <view class="no-data-tip">暂无信息</view>
  </block>
</view>

<!-- 入驻机构区域 -->
<view class="institution-section">
  <!-- 新的机构入驻引导条/标题 -->
  <view class="institution-apply-banner">
    <!-- 添加包裹元素应用标题样式 -->
    <view class="banner-text-wrapper" bindtap="navigateToInstitutionApply">
      <view class="banner-text">
        <view class="banner-title">机构入驻</view>
        <view class="banner-subtitle"></view>
      </view>
    </view>
    <view class="banner-link" bindtap="navigateToInstitutionApply">入驻登记 ></view>
  </view>

  <!-- 机构列表 -->
  <block wx:if="{{institutions.length > 0}}">
    <view class="institution-grid">
      <!-- 恢复类名以应用背景样式 -->
      <view class="institution-item" wx:for="{{institutions}}" wx:key="_id" bindtap="navigateToInstitutionDetail" data-id="{{item._id}}">
        <!-- 确认默认图片逻辑 -->
        <image class="institution-image" src="{{item.image || '/images/hospital.png'}}" mode="aspectFill"></image>
        <view class="institution-info">
          <view class="institution-name">{{item.name}} <text class="institution-address">{{item.province}}{{item.city}}{{item.district}}</text></view>
        </view>
      </view>
    </view>
  </block>
  <block wx:else>
    <view class="no-data-tip">暂无入驻机构</view>
  </block>
</view>

</view>