/* 整体容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(to bottom, #f0f7fa, #f9e3d7);
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #795548;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #a1887f;
}

/* 表单容器 */
.form-container {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

/* 图片上传项 */
.form-image-item {
  margin-bottom: 30rpx;
}

.image-container {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  background-color: #f9f5f2;
  margin-top: 10rpx;
  position: relative;
  overflow: hidden;
}

.institution-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.image-tip {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10rpx 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}

/* 表单项通用样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #795548;
  margin-bottom: 10rpx;
}

.required {
  color: #e8846e;
  font-weight: bold;
}

.form-input {
  height: 80rpx;
  background-color: #f9f5f2;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #5d4037;
  border: 1rpx solid #e0d6cd;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f9f5f2;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #5d4037;
  box-sizing: border-box;
  border: 1rpx solid #e0d6cd;
}

/* 地区选择器 */
.location-picker {
  display: flex;
  flex-wrap: wrap;
}

.location-item {
  flex: 1;
  min-width: 180rpx;
  height: 80rpx;
  background-color: #f9f5f2;
  border-radius: 10rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  border: 1rpx solid #e0d6cd;
}

.location-text {
  font-size: 28rpx;
  color: #795548;
}

.location-arrow {
  font-size: 24rpx;
  color: #a1887f;
}

/* 按钮容器 */
.btn-container {
  margin-top: auto;
  padding: 20rpx 0;
}

.save-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #f5a890, #e8846e);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(232, 132, 110, 0.3);
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 8rpx rgba(232, 132, 110, 0.2);
}

/* 地区选择器弹窗 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.picker-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  height: 60vh;
  z-index: 101;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
}

.picker-container.show {
  transform: translateY(0);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f3e0d6;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #795548;
}

.picker-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #a1887f;
}

.picker-list {
  flex: 1;
  overflow-y: auto;
}

.picker-item {
  padding: 30rpx;
  font-size: 30rpx;
  color: #5d4037;
  border-bottom: 1rpx solid #f3e0d6;
}

.picker-item.active {
  color: #e8846e;
  background-color: #f9f5f2;
  font-weight: bold;
}

.picker-empty {
  padding: 50rpx 0;
  text-align: center;
  color: #bcaaa4;
  font-size: 28rpx;
} 