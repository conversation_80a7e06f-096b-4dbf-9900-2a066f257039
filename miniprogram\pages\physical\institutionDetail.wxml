<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 错误信息 -->
  <view class="error-container" wx:elif="{{error}}">
    <icon type="warn" size="64" color="#e8846e"></icon>
    <view class="error-text">{{error}}</view>
    <button class="back-btn" bindtap="navigateBack">返回上一页</button>
  </view>
  
  <!-- 机构详情 -->
  <block wx:elif="{{institution}}">
    <!-- 封面图片 -->
    <view class="header-image-container">
      <image class="header-image" src="{{institution.image || '/images/hospital.png'}}" mode="aspectFill"></image>
      
      <!-- 管理员操作按钮 -->
      <view class="admin-buttons" wx:if="{{userRole === 'admin'}}">
        <button class="icon-btn edit-btn" bindtap="editInstitution">✏️</button>
        <button class="icon-btn delete-btn" bindtap="deleteInstitution">🗑️</button>
      </view>
    </view>
    
    <!-- 机构基本信息 -->
    <view class="info-card common-card">
      <view class="institution-name">{{institution.name}}</view>
      
      <!-- 地址信息 -->
      <view class="info-item">
        <view class="info-icon location-icon">📍</view>
        <view class="info-content">
          <view class="info-label">地址信息</view>
          <view class="info-value address-value">{{institution.province}}{{institution.city}}{{institution.district}}{{institution.address}}</view>
        </view>
      </view>
      
      <!-- 联系电话 -->
      <view class="info-item" wx:if="{{institution.phone}}">
        <view class="info-icon phone-icon">📞</view>
        <view class="info-content">
          <view class="info-label">联系方式</view>
          <view class="info-value">{{institution.phone}}</view>
        </view>
      </view>
      
      <!-- 服务对象 -->
      <view class="info-item" wx:if="{{institution.serviceTarget}}">
        <view class="info-icon target-icon">👥</view>
        <view class="info-content">
          <view class="info-label">服务对象</view>
          <view class="info-value">{{institution.serviceTarget}}</view>
        </view>
      </view>
    </view>
    
    <!-- 服务内容 -->
    <view class="services-card common-card" wx:if="{{institution.serviceItems}}">
      <view class="card-title">服务内容</view>
      <view class="services-content">{{institution.serviceItems}}</view>
    </view>
    
    <!-- 暂无服务内容提示 -->
    <view class="services-card common-card" wx:else>
      <view class="card-title">服务内容</view>
      <view class="services-placeholder">机构暂未提供详细服务内容，请直接与机构联系了解更多信息。</view>
    </view>
  </block>
</view> 