.knowledge-container {
  padding: 30rpx;
  background-color: #f8f0f8; /* 淡紫色背景 */
  min-height: 100vh;
}

.header {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(101, 75, 109, 0.15); /* 紫色调阴影 */
  margin-bottom: 30rpx;
  text-align: center;
  border-left: 8rpx solid #c4a1e8; /* 紫色左边框 */
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15rpx;
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #654b6d; /* 紫色标题 */
  margin-bottom: 12rpx;
}

.title-decoration {
  width: 80rpx;
  height: 6rpx;
  background-color: #c4a1e8; /* 紫色装饰线 */
  border-radius: 3rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #8a7e8a; /* 温暖的副标题颜色 */
  margin-top: 8rpx;
}

/* 管理员操作区域 */
.admin-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #c4a1e8; /* 紫色按钮 */
  color: #ffffff;
  font-size: 28rpx;
  padding: 10rpx 25rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(196, 161, 232, 0.3); /* 匹配按钮的阴影 */
  line-height: 1.5;
}

.add-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

/* 文章列表 */
.knowledge-list {
  margin-top: 25rpx;
}

.article-card {
  display: flex;
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 4rpx 10rpx rgba(101, 75, 109, 0.1); /* 紫色调阴影 */
  position: relative;
  border-bottom: 3rpx solid #f2e8f2; /* 淡紫色底部边框 */
}

.card-left {
  flex-shrink: 0;
  width: 180rpx;
  height: 180rpx;
  margin-right: 25rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.article-image {
  width: 100%;
  height: 100%;
}

.card-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}

.article-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #654b6d; /* 紫色标题 */
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-summary {
  font-size: 28rpx;
  color: #8a7e8a; /* 温暖的摘要文字颜色 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 12rpx;
}

.article-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #b3a8b3; /* 温暖的信息文字颜色 */
}

.article-author {
  max-width: 45%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 管理员操作按钮 */
.admin-buttons {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  display: flex;
}

.icon-btn {
  background: none;
  padding: 0;
  margin: 0 0 0 20rpx;
  border: none;
  outline: none;
  line-height: 1;
  font-size: 36rpx;
}

.icon-btn::after {
  display: none;
}

/* 空状态提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 220rpx;
  height: 220rpx;
  margin-bottom: 25rpx;
}

.empty-text {
  font-size: 34rpx;
  color: #8a7e8a; /* 温暖的文字颜色 */
  margin-bottom: 12rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #b3a8b3; /* 温暖的副文字颜色 */
}

/* 加载更多区域 */
.loading-container {
  text-align: center;
  padding: 25rpx 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #c4a1e8; /* 紫色加载点 */
  margin: 0 6rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.load-more {
  font-size: 28rpx;
  color: #c4a1e8; /* 紫色按钮文字 */
}

.no-more {
  font-size: 28rpx;
  color: #c9bfc9; /* 温暖的结束文字颜色 */
}

/* 顶部标签页样式 */
.tab-container {
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 10rpx 0;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(101, 75, 109, 0.12);
}

.tab {
  position: relative;
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  color: #8a7e8a;
  font-size: 30rpx;
  transition: all 0.3s;
}

.tab.active {
  color: #654b6d;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(to right, #c4a1e8, #a679d1);
  border-radius: 3rpx;
}

/* 心理测评列表样式 */
.assessment-list {
  padding: 20rpx 0;
}

.assessment-card {
  background-color: #ffffff;
  border-radius: 15rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(101, 75, 109, 0.15);
}

.assessment-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.assessment-content {
  padding: 25rpx;
}

.assessment-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #654b6d;
  margin-bottom: 15rpx;
}

.assessment-desc {
  font-size: 28rpx;
  color: #8a7e8a;
  line-height: 1.5;
  margin-bottom: 25rpx;
}

.assessment-btn {
  background: linear-gradient(to right, #c4a1e8, #a679d1);
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  border-radius: 30rpx;
  padding: 15rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(166, 121, 209, 0.3);
  width: fit-content;
  margin-left: auto;
  line-height: 1.5;
}

.assessment-btn::after {
  display: none;
} 