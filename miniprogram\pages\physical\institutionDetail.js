const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: '', // 机构ID
    institution: null, // 机构信息
    userRole: 'user', // 用户角色
    loading: true, // 加载状态
    error: '' // 错误信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    if (options.id) {
      this.setData({
        id: options.id,
        userRole: userRole
      });
      
      // 加载机构详情
      this.loadInstitutionDetail(options.id);
    } else {
      this.setData({
        loading: false,
        error: '机构ID不存在'
      });
    }
  },
  
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 如果有ID，重新加载数据
    if (this.data.id) {
      this.loadInstitutionDetail(this.data.id);
    }
  },
  
  // 加载机构详情
  loadInstitutionDetail: function(id) {
    this.setData({
      loading: true,
      error: ''
    });
    
    const db = wx.cloud.database();
    
    db.collection('institutions').doc(id).get().then(res => {
      this.setData({
        institution: res.data,
        loading: false
      });
    }).catch(err => {
      console.error('获取机构详情失败:', err);
      
      this.setData({
        loading: false,
        error: '获取机构信息失败'
      });
    });
  },
  
  // 拨打电话
  callPhone: function() {
    if (!this.data.institution || !this.data.institution.phone) {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      });
      return;
    }
    
    wx.makePhoneCall({
      phoneNumber: this.data.institution.phone
    });
  },
  
  // 查看地图位置
  viewLocation: function() {
    if (!this.data.institution) return;
    
    const institution = this.data.institution;
    const address = institution.province + institution.city + institution.district + institution.address;
    
    // 打开地图
    wx.openLocation({
      latitude: 39.90, // 默认纬度，实际应用中应该保存真实坐标
      longitude: 116.40, // 默认经度
      name: institution.name,
      address: address,
      scale: 18
    });
  },
  
  // 编辑机构
  editInstitution: function() {
    wx.navigateTo({
      url: `/pages/physical/institutionEdit?id=${this.data.id}`
    });
  },
  
  // 删除机构
  deleteInstitution: function() {
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除此机构吗？',
      success: res => {
        if (res.confirm) {
          const db = wx.cloud.database();
          
          wx.showLoading({
            title: '正在删除',
            mask: true
          });
          
          db.collection('institutions').doc(this.data.id).remove().then(() => {
            wx.hideLoading();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
            
          }).catch(err => {
            wx.hideLoading();
            console.error('删除机构失败:', err);
            
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    if (this.data.institution) {
      return {
        title: this.data.institution.name,
        path: `/pages/physical/institutionDetail?id=${this.data.id}`
      };
    }
    
    return {
      title: '安宁疗护机构详情',
      path: '/pages/physical/physicalIndex'
    };
  }
}) 