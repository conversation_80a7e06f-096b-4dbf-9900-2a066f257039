.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 表单头部 */
.form-header {
  padding: 40rpx;
  background-color: #4a9cf0;
  color: #ffffff;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.form-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 表单主体 */
.form-body {
  flex: 1;
  padding: 30rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.form-label text {
  font-size: 28rpx;
  color: #333;
}

.required {
  color: #ff4d4f;
  margin-right: 5rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
}

/* 图片上传 */
.form-upload {
  width: 200rpx;
  height: 200rpx;
  border: 1px dashed #d9d9d9;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

/* 地区选择器 */
.picker-content {
  width: 100%;
  height: 80rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.placeholder-text {
  color: #999;
}

.picker-arrow {
  color: #999;
  font-size: 30rpx;
}

/* 提交按钮 */
.form-actions {
  margin-top: 50rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #4a9cf0;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
} 