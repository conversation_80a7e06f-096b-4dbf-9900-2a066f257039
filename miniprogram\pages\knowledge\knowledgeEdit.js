const app = getApp();

Page({
  data: {
    isLoading: false, // 是否正在加载
    isSaving: false, // 是否正在保存
    isEdit: false, // 是否是编辑模式
    article: {
      title: '',
      author: '',
      summary: '',
      content: '',
      coverImage: ''
    },
    tempCoverPath: '', // 临时封面图片路径
    cursorPosition: null, // 光标位置
    category: 'knowledge' // 文章分类
  },

  onLoad: function (options) {
    // 检查用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    if (userRole !== 'admin') {
      wx.showToast({
        title: '您没有权限编辑文章',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
      return;
    }
    
    // 获取文章分类（如果有）
    let category = options.category || 'knowledge';
    
    if (options.id) {
      // 编辑现有文章
      this.setData({
        isEdit: true,
        isLoading: true
      });
      
      this.fetchArticle(options.id);
    } else {
      // 作者默认为当前用户昵称
      const userInfo = wx.getStorageSync('userInfo');
      
      if (userInfo && userInfo.nickName) {
        let article = this.data.article;
        article.author = userInfo.nickName;
        article.category = category; // 设置文章分类
        
        this.setData({
          article: article
        });
      }
    }
  },

  // 获取文章详情
  fetchArticle: function (id) {
    const db = wx.cloud.database();
    
    db.collection('knowledge').doc(id).get().then(res => {
      this.setData({
        article: res.data,
        isLoading: false
      });
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: this.data.isEdit ? '编辑文章' : '新建文章'
      });
    }).catch(err => {
      console.error('获取文章详情失败:', err);
      
      this.setData({
        isLoading: false
      });
      
      wx.showToast({
        title: '获取文章失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },

  // 标题输入处理
  onTitleInput: function (e) {
    let article = this.data.article;
    article.title = e.detail.value;
    
    this.setData({
      article: article
    });
  },

  // 作者输入处理
  onAuthorInput: function (e) {
    let article = this.data.article;
    article.author = e.detail.value;
    
    this.setData({
      article: article
    });
  },

  // 摘要输入处理
  onSummaryInput: function (e) {
    let article = this.data.article;
    article.summary = e.detail.value;
    
    this.setData({
      article: article
    });
  },

  // 内容输入处理
  onContentInput: function (e) {
    let article = this.data.article;
    article.content = e.detail.value;
    
    // 保存光标位置
    if (e.detail.cursor !== undefined) {
      this.setData({
        article: article,
        cursorPosition: e.detail.cursor
      });
    } else {
      this.setData({
        article: article
      });
    }
  },

  // 选择封面图片
  chooseImage: function () {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'],
      success: res => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        const fileSize = res.tempFiles[0].size;
        
        // 限制图片大小为2MB
        const maxSize = 5 * 1024 * 1024;
        if (fileSize > maxSize) {
          wx.showToast({
            title: '图片不能超过5MB',
            icon: 'none'
          });
          return;
        }
        
        this.setData({
          tempCoverPath: tempFilePath
        });
      }
    });
  },

  // 移除封面图片
  removeCover: function () {
    let article = this.data.article;
    
    this.setData({
      tempCoverPath: '',
      ['article.coverImage']: ''
    });
  },

  // 设置标题样式
  setHeading: function (e) {
    const level = e.currentTarget.dataset.level;
    const content = this.data.article.content;
    const selectedText = this.getSelectedText() || '请输入标题';
    
    // 插入HTML标签
    const headingText = `<h${level}>${selectedText}</h${level}>`;
    
    // 更新内容
    let article = this.data.article;
    article.content = this.insertTextAtCursor(content, headingText);
    
    this.setData({
      article: article
    });
  },

  // 设置粗体
  setBold: function () {
    const content = this.data.article.content;
    const selectedText = this.getSelectedText() || '粗体文本';
    
    // 插入HTML标签
    const boldText = `<strong>${selectedText}</strong>`;
    
    // 更新内容
    let article = this.data.article;
    article.content = this.insertTextAtCursor(content, boldText);
    
    this.setData({
      article: article
    });
  },

  // 设置斜体
  setItalic: function () {
    const content = this.data.article.content;
    const selectedText = this.getSelectedText() || '斜体文本';
    
    // 插入HTML标签
    const italicText = `<em>${selectedText}</em>`;
    
    // 更新内容
    let article = this.data.article;
    article.content = this.insertTextAtCursor(content, italicText);
    
    this.setData({
      article: article
    });
  },

  // 设置下划线
  setUnderline: function () {
    const content = this.data.article.content;
    const selectedText = this.getSelectedText() || '下划线文本';
    
    // 插入HTML标签
    const underlineText = `<u>${selectedText}</u>`;
    
    // 更新内容
    let article = this.data.article;
    article.content = this.insertTextAtCursor(content, underlineText);
    
    this.setData({
      article: article
    });
  },

// 插入图片
insertImage: function () {
  wx.chooseMedia({
    count: 1,
    mediaType: ['image'],
    sourceType: ['album', 'camera'],
    sizeType: ['compressed'],
    success: res => {
      const tempFilePath = res.tempFiles[0].tempFilePath;
      
      // 上传图片到云存储
      wx.showLoading({
        title: '上传图片中',
        mask: true
      });
      
      const cloudPath = `knowledge/${new Date().getTime()}_${Math.random().toString(36).slice(-6)}.jpg`;
      
      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath,
        success: res => {
          // 获取cloud文件ID
          const cloudFileID = res.fileID;
          
          // 获取临时可访问链接
          wx.cloud.getTempFileURL({
            fileList: [cloudFileID],
            success: result => {
              wx.hideLoading();
              
              if (result.fileList && result.fileList.length > 0) {
                const httpUrl = result.fileList[0].tempFileURL;
                
                // 处理图片尺寸
                wx.getImageInfo({
                  src: tempFilePath,
                  success: imgInfo => {
                    // 使用居中显示的样式
                    const imageTag = `<div style="text-align:center;"><img src="${httpUrl}" data-cloud-id="${cloudFileID}" style="max-width:100%;height:auto;" /></div>`;
                    
                    // 更新内容
                    let article = this.data.article;
                    article.content = this.insertTextAtCursor(article.content, imageTag);
                    
                    this.setData({
                      article: article
                    });
                    
                    wx.showToast({
                      title: '图片已上传成功',
                      icon: 'success'
                    });
                  },
                  fail: () => {
                    // 无法获取图片信息时使用默认标签
                    const imageTag = `<div style="text-align:center;"><img src="${httpUrl}" data-cloud-id="${cloudFileID}" style="max-width:100%;" /></div>`;
                    
                    // 更新内容
                    let article = this.data.article;
                    article.content = this.insertTextAtCursor(article.content, imageTag);
                    
                    this.setData({
                      article: article
                    });
                    
                    wx.showToast({
                      title: '图片已上传成功',
                      icon: 'success'
                    });
                  }
                });
              } else {
                wx.showToast({
                  title: '获取图片链接失败',
                  icon: 'none'
                });
              }
            },
            fail: err => {
              wx.hideLoading();
              console.error('获取临时链接失败:', err);
              wx.showToast({
                title: '获取图片链接失败',
                icon: 'none'
              });
            }
          });
        },
        fail: err => {
          wx.hideLoading();
          console.error('上传图片失败:', err);
          
          wx.showToast({
            title: '上传图片失败',
            icon: 'none'
          });
        }
      });
    }
  });
},

  // 插入列表
  insertList: function () {
    const content = this.data.article.content;
    
    // 插入列表标签
    const listTag = '<ul>\n  <li>列表项 1</li>\n  <li>列表项 2</li>\n  <li>列表项 3</li>\n</ul>';
    
    // 更新内容
    let article = this.data.article;
    article.content = this.insertTextAtCursor(content, listTag);
    
    this.setData({
      article: article
    });
  },

  // 获取选中的文本（注：小程序中无法直接获取，此方法仅为占位）
  getSelectedText: function () {
    // 在小程序无法获取选中文本，这里返回空
    
    return '';
  },
  

  // 在光标位置插入文本（注：小程序中无法直接操作，此方法是模拟实现）
  insertTextAtCursor: function (originalText, newText) {
    // 修改为在内容中间插入而不是追加到末尾
    const cursorPosition = this.data.cursorPosition || originalText.length;
    const beforeCursor = originalText.substring(0, cursorPosition);
    const afterCursor = originalText.substring(cursorPosition);
    return beforeCursor + newText + afterCursor;
  },

  // 保存文章
  saveArticle: function () {
    // 验证表单
    if (!this.data.article.title) {
      wx.showToast({
        title: '请输入文章标题',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.article.author) {
      wx.showToast({
        title: '请输入作者名称',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.article.content) {
      wx.showToast({
        title: '请输入文章内容',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      isSaving: true
    });
    
    // 先上传封面图片（如果有）
    if (this.data.tempCoverPath) {
      this.uploadCoverImage().then(fileID => {
        this.data.article.coverImage = fileID;
        return this.saveArticleToDatabase();
      }).catch(err => {
        console.error('上传封面图片失败:', err);
        
        this.setData({
          isSaving: false
        });
        
        wx.showToast({
          title: '上传封面图片失败',
          icon: 'none'
        });
      });
    } else {
      // 没有封面图片，直接保存到数据库
      this.saveArticleToDatabase().catch(err => {
        console.error('保存文章失败:', err);
        
        this.setData({
          isSaving: false
        });
        
        wx.showToast({
          title: '保存文章失败',
          icon: 'none'
        });
      });
    }
  },
  switchEditMode: function(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      editMode: mode
    });
  },
  // 上传封面图片
  uploadCoverImage: function () {
    const filePath = this.data.tempCoverPath;
    
    wx.showLoading({
      title: '上传封面中',
      mask: true
    });
    
    const cloudPath = `knowledge/covers/${new Date().getTime()}_${Math.random().toString(36).slice(-6)}.jpg`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: res => {
        // 更新封面图片URL
        let article = this.data.article;
        article.coverImage = res.fileID;
        
        this.setData({
          article: article,
          tempCoverPath: ''
        });
        
        wx.hideLoading();
        
        // 继续保存文章
        return res.fileID;
      },
      fail: err => {
        wx.hideLoading();
        console.error('上传封面失败:', err);
        
        this.setData({
          isSaving: false
        });
        
        wx.showToast({
          title: '上传封面失败',
          icon: 'none'
        });
      }
    });
  },

  // 保存文章到数据库
  saveArticleToDatabase: function () {
    const db = wx.cloud.database();
    
    // 确保文章有分类标识
    if (!this.data.article.category) {
      this.data.article.category = 'knowledge';
    }
    
    // 设置文章创建时间
    if (!this.data.isEdit) {
      this.data.article.createTime = db.serverDate();
    }
    
    // 设置文章更新时间
    this.data.article.updateTime = db.serverDate();
    
    // 如果没有摘要，则自动生成摘要
    if (!this.data.article.summary) {
      // 提取内容中的纯文本作为摘要，最多100个字符
      const plainText = this.data.article.content.replace(/<[^>]+>/g, '');
      this.data.article.summary = plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText;
    }
    
    // 保存到数据库
    if (this.data.isEdit) {
      // 更新现有文章
      return db.collection('knowledge').doc(this.data.article._id).update({
        data: {
          title: this.data.article.title,
          author: this.data.article.author,
          summary: this.data.article.summary,
          content: this.data.article.content,
          coverImage: this.data.article.coverImage,
          updateTime: this.data.article.updateTime,
          category: this.data.article.category // 确保更新分类标识
        }
      }).then(() => {
        this.setData({
          isSaving: false
        });
        
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      });
    } else {
      // 创建新文章
      return db.collection('knowledge').add({
        data: {
          title: this.data.article.title,
          author: this.data.article.author,
          summary: this.data.article.summary,
          content: this.data.article.content,
          coverImage: this.data.article.coverImage,
          createTime: this.data.article.createTime,
          updateTime: this.data.article.updateTime,
          category: this.data.article.category // 设置分类标识
        }
      }).then(() => {
        this.setData({
          isSaving: false
        });
        
        wx.showToast({
          title: '发布成功',
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      });
    }
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack();
  },

  // 设置文本颜色
  setColor: function (e) {
    const color = e.currentTarget.dataset.color;
    const content = this.data.article.content;
    const selectedText = this.getSelectedText() || '有颜色的文本';
    
    // 插入带颜色的HTML标签
    const coloredText = `<span style="color: ${color};">${selectedText}</span>`;
    
    // 更新内容
    let article = this.data.article;
    article.content = this.insertTextAtCursor(content, coloredText);
    
    this.setData({
      article: article
    });
  }
}); 