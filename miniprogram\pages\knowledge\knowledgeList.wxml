<view class="knowledge-container">
  <!-- 顶部标签页切换 -->
  <view class="tab-container">
    <view class="tab {{activeTab === 'knowledge' ? 'active' : ''}}" bindtap="switchTab" data-tab="knowledge">
      <text>图文知识</text>
      <view class="tab-line" wx:if="{{activeTab === 'knowledge'}}"></view>
    </view>
    <view class="tab {{activeTab === 'wishpool' ? 'active' : ''}}" bindtap="switchTab" data-tab="wishpool">
      <text>许愿池</text>
      <view class="tab-line" wx:if="{{activeTab === 'wishpool'}}"></view>
    </view>
  </view>

  <!-- 图文知识内容区 -->
  <block wx:if="{{activeTab === 'knowledge'}}">
    <!-- 管理员添加文章按钮 -->
    <view class="admin-actions" wx:if="{{userRole === 'admin'}}">
      <button class="add-btn" bindtap="navigateToEdit">
        <text class="add-icon">+</text>
        <text>添加文章</text>
      </button>
    </view>

    <!-- 文章列表 -->
    <view class="knowledge-list">
      <view wx:if="{{articleList.length === 0}}" class="empty-tip">
        <image class="empty-image" src="/images/empty.png" mode="aspectFit"></image>
        <text class="empty-text">暂无内容</text>
        <text class="empty-subtext">我们正在努力更新中...</text>
      </view>

      <view wx:for="{{articleList}}" 
            wx:key="_id" 
            class="article-card"
            bindtap="navigateToDetail"
            data-id="{{item._id}}">
        <view class="card-left">
          <image class="article-image" 
                src="{{item.coverImage || '/images/doctor.png'}}" 
                mode="aspectFill"></image>
        </view>
        <view class="card-right">
          <view class="article-title">{{item.title}}</view>
          <view class="article-summary">{{item.summary}}</view>
          <view class="article-info">
            <text class="article-author">{{item.author || '管理员'}}</text>
            <text class="article-time">{{item.createTime}}</text>
          </view>
          <!-- 管理员操作按钮 -->
          <view class="admin-buttons" wx:if="{{userRole === 'admin'}}" catchtap="stopPropagation">
            <button class="icon-btn edit-btn" catchtap="navigateToEdit" data-id="{{item._id}}">✏️</button>
            <button class="icon-btn delete-btn" catchtap="deleteArticle" data-id="{{item._id}}">🗑️</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多区域 -->
    <view class="loading-container" wx:if="{{articleList.length > 0}}">
      <view wx:if="{{isLoading}}" class="loading">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
      <text wx:elif="{{hasMore}}" class="load-more" bindtap="loadMore">加载更多</text>
      <text wx:else class="no-more">- 已经到底啦 -</text>
    </view>
  </block>

  <!-- 许愿池内容区 -->
  <block wx:if="{{activeTab === 'wishpool'}}">
    <text class="coming-soon-message">功能开发中，敬请期待...</text>
  </block>
</view> 