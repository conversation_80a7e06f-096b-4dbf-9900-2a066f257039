.join-container {
  padding: 30rpx;
  background-color: #f5f0f8; /* 淡紫粉色背景 */
  min-height: 100vh;
}

.header {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(109, 75, 125, 0.15); /* 紫粉色调阴影 */
  margin-bottom: 30rpx;
  text-align: center;
  border-left: 8rpx solid #d8a1e8; /* 紫粉色左边框 */
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15rpx;
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #6d4b7d; /* 紫粉色标题 */
  margin-bottom: 12rpx;
}

.title-decoration {
  width: 80rpx;
  height: 6rpx;
  background-color: #d8a1e8; /* 紫粉色装饰线 */
  border-radius: 3rpx;
}

/* 内容区域 */
.content-section {
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(109, 75, 125, 0.15);
  padding: 30rpx;
  margin-bottom: 30rpx;
}

/* 管理员操作区域 */
.admin-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #d8a1e8; /* 紫粉色按钮 */
  color: #ffffff;
  font-size: 28rpx;
  padding: 10rpx 25rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(216, 161, 232, 0.3); /* 匹配按钮的阴影 */
  line-height: 1.5;
}

.edit-icon {
  margin-right: 8rpx;
}

/* 内容展示 */
.content-view {
  display: flex;
  flex-direction: column;
}

.content-text {
  font-size: 32rpx;
  color: #6d4b7d;
  line-height: 1.8;
  text-align: justify;
  white-space: pre-wrap;
  width: 100%;
  margin-bottom: 30rpx;
}

.images-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.image-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-image {
  width: 100%;
  max-width: 500rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.image-tip {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #8a7e8f;
}

/* 编辑模式 */
.edit-view {
  display: flex;
  flex-direction: column;
}

.text-edit {
  margin-bottom: 30rpx;
}

.text-area {
  width: 100%;
  height: 300rpx;
  box-sizing: border-box;
  padding: 20rpx;
  font-size: 32rpx;
  line-height: 1.6;
  color: #6d4b7d;
  border: 2rpx solid #d8a1e8;
  border-radius: 10rpx;
}

.images-edit {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx dashed #d8a1e8;
  padding: 20rpx;
  border-radius: 10rpx;
}

.upload-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #6d4b7d;
  margin-bottom: 15rpx;
  align-self: flex-start;
}

.preview-image {
  width: 100%;
  max-width: 500rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.upload-btn {
  background-color: #d8a1e8;
  color: #ffffff;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 8rpx rgba(216, 161, 232, 0.3);
}

.action-buttons {
  display: flex;
  justify-content: space-between;
}

.cancel-btn, .save-btn {
  width: 320rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #666666;
}

.save-btn {
  background-color: #d8a1e8;
  color: #ffffff;
} 