const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    pageTitle: '心理板块',
    pageDescription: '心理支持与情绪疏导',
    articleList: [],
    isLoading: false,
    hasMore: true,
    pageSize: 10,
    skip: 0,
    userRole: 'user',
    category: 'psychology',
    
    // Tab相关数据
    activeTab: 'knowledge', // 默认显示图文知识
    
    // 心理测评相关数据
    assessmentList: [
      {
        id: 1,
        title: '抑郁情绪自评量表',
        description: '抑郁自评量表(Self-rating depression scale, SDS)是国际通用的抑郁自评量表，用于评定抑郁状态和抑郁程度的变化',
        imageUrl: '/images/newspaper.png',
        shortLink: '#小程序://心理测试/EnjOYL0nPBUAktw'
      },
     
      {
        id: 2,
        title: '痴呆简易筛查量表',
        description: '痴呆简易筛查量表（Brief Screening Scale for Dementia，BSSD）是用于快速筛查痴呆症状的工具',
        imageUrl: '/images/newspaper.png',
        shortLink: '#小程序://心理测试/DfFyt3HE8bfhaYC'
      },
      {
        id: 3,
        title: '阿尔茨海默病（AD）',
        description: '阿尔茨海默病（AD）测评用于评估与阿尔茨海默病相关的认知功能和行为变化',
        imageUrl: '/images/doctor.png',
        shortLink: '#小程序://心理测试/OPTDtl3JBC8qZIz'
      },
      {
        id: 4,
        title: '老年抑郁测评',
        description: '老年抑郁测评（Geriatric Depression Scale, GDS）是专门用于评估老年人抑郁症状的量表',
        imageUrl: '/images/hospital.png',
        shortLink: '#小程序://心理测试/mrzyZMOujpgshQu'
      },
      {
        id: 5,
        title: '纽芬兰纪念大学幸福度量表',
        description: '纽芬兰纪念大学幸福度量表用于评估个体主观幸福感和生活满意度',
        imageUrl: '/images/newspaper.png',
        shortLink: '#小程序://心理测试/A0SU5XpmfLLBvnJ'
      },
      {
        id: 6,
        title: '90项症状清单',
        description: '90项症状清单（Symptom Checklist-90, SCL-90）是一种自我评估心理健康状态的常用量表',
        imageUrl: '/images/doctor.png',
        shortLink: '#小程序://心理测试/d8ukSlwSFL2pDtD'
      },
      {
        id: 7,
        title: '抑郁体验问卷',
        description: '抑郁体验问卷（Depressive Experiences Questionnaire，DEQ）用于评估抑郁体验的不同维度',
        imageUrl: '/images/hospital.png',
        shortLink: '#小程序://心理测试/NBARy4fVPqTxLWC'
      },
      {
        id: 8,
        title: '孤独量表',
        description: '孤独量表（UCLA Loneliness Scale，UCLA）用于评估个体感受到的社交孤独程度',
        imageUrl: '/images/newspaper.png',
        shortLink: '#小程序://心理测试/ug1yq9uITkpAVeq'
      },
      {
        id: 9,
        title: '汉密顿抑郁量表',
        description: '汉密顿抑郁量表（Hamilton Depression Scale，HAMD）是临床上评估抑郁症状严重程度的量表',
        imageUrl: '/images/doctor.png',
        shortLink: '#小程序://心理测试/YxQVpALE9H9bgta'
      },
      {
        id: 10,
        title: '抑郁-焦虑-压力自评',
        description: '抑郁-焦虑-压力自评（DASS-21）是一种简短的自评量表，用于评估抑郁、焦虑和压力三个维度',
        imageUrl: '/images/hospital.png',
        shortLink: '#小程序://心理测试/R4YPLK9A5gBooso'
      },
      {
        id: 11,
        title: '轻躁狂检测清单',
        description: '轻躁狂检测清单（HCL-32）用于筛查轻躁狂症状，辅助双相情感障碍的诊断',
        imageUrl: '/images/newspaper.png',
        shortLink: '#小程序://心理测试/ExuNvJBb1gv6flG'
      },
      {
        id: 12,
        title: '贝克抑郁自评量表',
        description: '贝克抑郁自评量表（BDI）是评估抑郁症状严重程度的国际通用量表',
        imageUrl: '/images/doctor.png',
        shortLink: '#小程序://心理测试/7wZBhQnI2CG59Fv'
      },
      {
        id: 13,
        title: '贝克自杀观念量表',
        description: '贝克自杀观念量表(Beck Scale for Suicide Ideation, BSS)用于评估自杀意念和自杀风险',
        imageUrl: '/images/hospital.png',
        shortLink: '#小程序://心理测试/hdOl5MV7OpZ5QOp'
      },
      {
        id: 14,
        title: '汉密尔顿焦虑量表',
        description: '汉密尔顿焦虑量表（Hamilton Anxiety Scale，HAMA）是临床上常用的焦虑症状评估工具',
        imageUrl: '/images/newspaper.png',
        shortLink: '#小程序://心理测试/QvZ84T84BxGnmAG'
      },
      {
        id: 15,
        title: '贝克焦虑自评量表',
        description: '贝克焦虑自评量表（Beck Anxiety Inventory, BAI）用于评估个体所经历的焦虑症状的严重程度',
        imageUrl: '/images/doctor.png',
        shortLink: '#小程序://心理测试/r7utxo5DjjjDYvi'
      },
      {
        id: 16,
        title: '医院焦虑抑郁量表',
        description: '医院焦虑抑郁量表（Hospital Anxiety and Depression Scale, HADS）适用于评估非精神科患者的焦虑和抑郁症状',
        imageUrl: '/images/hospital.png',
        shortLink: '#小程序://心理测试/bgwJnnKhbn6beWf'
      },
      {
        id: 17,
        title: '状态-特质焦虑问卷',
        description: '状态-特质焦虑问卷（State-Trait Anxiety Inventory, STAI）用于区分评估当前焦虑状态和长期焦虑特质',
        imageUrl: '/images/newspaper.png',
        shortLink: '#小程序://心理测试/ydsD9FaaDkZHeCo'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    this.setData({
      userRole: userRole
    });
    
    // 加载文章列表
    this.loadArticles();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示页面时重新加载文章列表
    if (this.data.activeTab === 'knowledge') {
      this.setData({
        articleList: [],
        skip: 0,
        hasMore: true
      });
      this.loadArticles();
    }
  },

  // 切换Tab
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.activeTab) return;
    
    this.setData({
      activeTab: tab
    });
    
    // 如果切换到知识tab并且文章列表为空，则加载文章
    if (tab === 'knowledge' && this.data.articleList.length === 0) {
      this.loadArticles();
    }
  },

  // 跳转到测评小程序
  goToAssessment: function(e) {
    const shortLink = e.currentTarget.dataset.link;
    
    wx.navigateToMiniProgram({
      shortLink: shortLink,
      envVersion: 'release', // 正式版
      success(res) {
        console.log('成功打开测评小程序');
      },
      fail(err) {
        console.error('打开测评小程序失败', err);
        wx.showToast({
          title: '打开小程序失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载文章列表
  loadArticles: function () {
    if (this.data.isLoading || !this.data.hasMore) return;
    
    this.setData({
      isLoading: true
    });
    
    const db = wx.cloud.database();
    
    // 查询文章列表，按创建时间降序排列，只显示psychology类别的文章
    db.collection('knowledge')
      .where({
        category: this.data.category
      })
      .orderBy('createTime', 'desc')
      .skip(this.data.skip)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        // 格式化时间
        const articles = res.data.map(item => {
          // 格式化时间为：YYYY-MM-DD
          if (item.createTime) {
            let date = new Date(item.createTime);
            item.createTime = date.getFullYear() + '-' + 
                           ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
                           ('0' + date.getDate()).slice(-2);
          }
          
          // 确保有摘要
          if (!item.summary) {
            // 截取内容前100个字符作为摘要
            item.summary = item.content ? item.content.replace(/<[^>]+>/g, '').substring(0, 100) : '暂无摘要';
          }
          
          return item;
        });
        
        // 更新数据
        this.setData({
          articleList: [...this.data.articleList, ...articles],
          isLoading: false,
          skip: this.data.skip + articles.length,
          hasMore: articles.length === this.data.pageSize
        });
      })
      .catch(err => {
        console.error('获取文章列表失败:', err);
        this.setData({
          isLoading: false
        });
        
        wx.showToast({
          title: '获取文章失败',
          icon: 'none'
        });
      });
  },

  // 加载更多
  loadMore: function () {
    this.loadArticles();
  },

  // 阻止事件冒泡
  stopPropagation: function (e) {
    return false;
  },

  // 跳转到文章详情页
  navigateToDetail: function (e) {
    const articleId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/knowledge/knowledgeDetail?id=${articleId}`
    });
  },

  // 跳转到文章编辑页
  navigateToEdit: function (e) {
    // 只有管理员可以编辑
    if (this.data.userRole !== 'admin') {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }
    
    let url = `/pages/knowledge/knowledgeEdit?category=${this.data.category}`;
    
    // 如果包含文章ID，则为编辑现有文章
    if (e.currentTarget.dataset.id) {
      url += `&id=${e.currentTarget.dataset.id}`;
    }
    
    wx.navigateTo({
      url: url
    });
  },

  // 删除文章
  deleteArticle: function (e) {
    const articleId = e.currentTarget.dataset.id;
    
    // 只有管理员可以删除
    if (this.data.userRole !== 'admin') {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这篇文章吗？此操作不可恢复。',
      confirmColor: '#c4a1e8',
      success: res => {
        if (res.confirm) {
          const db = wx.cloud.database();
          
          db.collection('knowledge')
            .doc(articleId)
            .remove()
            .then(res => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              
              // 从本地数据中删除
              const updatedArticles = this.data.articleList.filter(item => item._id !== articleId);
              this.setData({
                articleList: updatedArticles
              });
            })
            .catch(err => {
              console.error('删除文章失败:', err);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: this.data.pageTitle,
      path: '/pages/psychology/psychologyIndex'
    }
  }
}) 