<view class="knowledge-container">
  <!-- 顶部标签页切换 -->
  <view class="tab-container">
    <view class="tab {{activeTab === 'knowledge' ? 'active' : ''}}" bindtap="switchTab" data-tab="knowledge">
      <text>图文知识</text>
      <view class="tab-line" wx:if="{{activeTab === 'knowledge'}}"></view>
    </view>
    <view class="tab {{activeTab === 'institution' ? 'active' : ''}}" bindtap="switchTab" data-tab="institution">
      <text>安宁疗护机构</text>
      <view class="tab-line" wx:if="{{activeTab === 'institution'}}"></view>
    </view>
  </view>

  <!-- 图文知识内容区 -->
  <block wx:if="{{activeTab === 'knowledge'}}">
    <!-- 管理员添加文章按钮 -->
    <view class="admin-actions" wx:if="{{userRole === 'admin'}}">
      <button class="add-btn" bindtap="navigateToEdit">
        <text class="add-icon">+</text>
        <text>添加文章</text>
      </button>
    </view>

    <!-- 文章列表 -->
    <view class="knowledge-list">
      <view wx:if="{{articleList.length === 0}}" class="empty-tip">
        <image class="empty-image" src="/images/empty.png" mode="aspectFit"></image>
        <text class="empty-text">暂无内容</text>
        <text class="empty-subtext">我们正在努力更新中...</text>
      </view>

      <view wx:for="{{articleList}}" 
            wx:key="_id" 
            class="article-card"
            bindtap="navigateToDetail"
            data-id="{{item._id}}">
        <view class="card-left">
          <image class="article-image" 
                src="{{item.coverImage || '/images/doctor.png'}}" 
                mode="aspectFill"></image>
        </view>
        <view class="card-right">
          <view class="article-title">{{item.title}}</view>
          <view class="article-summary">{{item.summary}}</view>
          <view class="article-info">
            <text class="article-author">{{item.author || '管理员'}}</text>
            <text class="article-time">{{item.createTime}}</text>
          </view>
          <!-- 管理员操作按钮 -->
          <view class="admin-buttons" wx:if="{{userRole === 'admin'}}" catchtap="stopPropagation">
            <button class="icon-btn edit-btn" catchtap="navigateToEdit" data-id="{{item._id}}">✏️</button>
            <button class="icon-btn delete-btn" catchtap="deleteArticle" data-id="{{item._id}}">🗑️</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多区域 -->
    <view class="loading-container" wx:if="{{articleList.length > 0}}">
      <view wx:if="{{isLoading}}" class="loading">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
      <text wx:elif="{{hasMore}}" class="load-more" bindtap="loadMore">加载更多</text>
      <text wx:else class="no-more">- 已经到底啦 -</text>
    </view>
  </block>

  <!-- 安宁疗护机构内容区 -->
  <block wx:if="{{activeTab === 'institution'}}">
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <icon type="search" size="16" color="#999"></icon>
        <input type="text" placeholder="请输入机构名称、地址等关键词" bindinput="onSearchInput" bindconfirm="onSearchConfirm" value="{{searchKeyword}}"/>
        <icon wx:if="{{searchKeyword}}" type="clear" size="16" color="#999" bindtap="clearSearch" class="clear-icon"></icon>
      </view>
    </view>

    <!-- 城市选择区域 -->
    <view class="location-container">
      <!-- 左侧省份列表（竖向滚动） -->
      <scroll-view scroll-y class="province-list" enhanced show-scrollbar="{{false}}">
        <view 
          wx:for="{{allProvinces}}" 
          wx:key="index" 
          class="province-item {{selectedProvince === item ? 'selected' : ''}}"
          bindtap="selectProvince"
          data-province="{{item}}">
          {{item}}
        </view>
      </scroll-view>

      <!-- 右侧城市展示区域 -->
      <view class="city-district-container">
        <!-- 当前选中的省份 -->
        <view class="selected-province">
          <text>{{selectedProvince}}</text>
        </view>
        
        <!-- 城市横向滚动列表 -->
        <scroll-view scroll-x class="city-scroll" enhanced show-scrollbar="{{false}}">
          <view class="city-scroll-container">
            <view 
              wx:if="{{availableCities.length === 0}}" 
              class="no-city-tip">
              暂无数据
            </view>
            <view 
              wx:for="{{availableCities}}" 
              wx:key="index" 
              class="city-item {{selectedCity === item ? 'selected' : ''}}"
              bindtap="selectCity"
              data-city="{{item}}">
              {{item}}
            </view>
          </view>
        </scroll-view>

        <!-- 机构列表 -->
        <scroll-view scroll-y class="institution-scroll">
          <view class="institution-list">
            <view wx:if="{{filteredInstitutions.length === 0}}" class="empty-tip">
              <image class="empty-image" src="/images/empty.png" mode="aspectFit"></image>
              <text class="empty-text">暂无机构信息</text>
              <text class="empty-subtext">我们正在努力收集中...</text>
            </view>

            <view wx:for="{{filteredInstitutions}}" 
                  wx:key="_id" 
                  class="institution-card"
                  bindtap="navigateToInstitutionDetail"
                  data-id="{{item._id}}">
              <view class="card-left">
                <image class="institution-image" 
                      src="{{item.image || '/images/hospital.png'}}" 
                      mode="aspectFill"></image>
              </view>
              <view class="card-right">
                <view class="institution-title">{{item.name}}</view>
                <view class="institution-address">{{item.province}}{{item.city}}{{item.district}}{{item.address}}</view>
                
                <!-- 管理员操作按钮 -->
                <view class="admin-institution-actions" wx:if="{{userRole === 'admin'}}" catchtap="stopPropagation">
                  <button class="institution-action-btn edit-btn" catchtap="editInstitution" data-id="{{item._id}}">✎</button>
                  <button class="institution-action-btn delete-btn" catchtap="deleteInstitution" data-id="{{item._id}}">✖</button>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 管理员添加机构按钮 -->
    <view class="add-institution-btn" wx:if="{{userRole === 'admin'}}" bindtap="navigateToInstitutionEdit">+</view>
  </block>
</view> 