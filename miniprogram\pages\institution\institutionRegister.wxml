<view class="container">
  <view class="form-header">
    <text class="form-title">安宁疗护机构登记</text>
    <text class="form-subtitle">请填写机构相关信息，带*的为必填项</text>
  </view>

  <view class="form-body">
    <!-- 机构基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <!-- 机构名称 -->
      <view class="form-item">
        <view class="form-label">
          <text class="required">*</text>
          <text>机构名称</text>
        </view>
        <input class="form-input" placeholder="请输入机构全称" value="{{formData.name}}" bindinput="inputChange" data-field="name"/>
      </view>
      
      <!-- 机构图片上传 -->
      <view class="form-item">
        <view class="form-label">
          <text>机构图片</text>
        </view>
        <view class="form-upload" bindtap="chooseImage">
          <image class="upload-image" src="{{formData.image || '/images/upload.png'}}" mode="aspectFill"></image>
          <text class="upload-text" wx:if="{{!formData.image}}">点击上传图片</text>
        </view>
      </view>
      
      <!-- 所在地区 -->
      <view class="form-item">
        <view class="form-label">
          <text class="required">*</text>
          <text>所在地区</text>
        </view>
        <picker mode="region" bindchange="regionChange" value="{{region}}">
          <view class="picker-content">
            <text wx:if="{{region[0] && region[1] && region[2]}}">{{region[0]}} {{region[1]}} {{region[2]}}</text>
            <text wx:else class="placeholder-text">请选择省/市/区县</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
      
      <!-- 详细地址 -->
      <view class="form-item">
        <view class="form-label">
          <text class="required">*</text>
          <text>详细地址</text>
        </view>
        <input class="form-input" placeholder="请输入详细地址" value="{{formData.address}}" bindinput="inputChange" data-field="address"/>
      </view>
      
      <!-- 联系电话 -->
      <view class="form-item">
        <view class="form-label">
          <text class="required">*</text>
          <text>联系电话</text>
        </view>
        <input class="form-input" type="number" placeholder="请输入联系电话" value="{{formData.phone}}" bindinput="inputChange" data-field="phone"/>
      </view>
    </view>
    
    <!-- 服务信息 -->
    <view class="form-section">
      <view class="section-title">服务信息</view>
      
      <!-- 服务对象 -->
      <view class="form-item">
        <view class="form-label">
          <text>服务对象</text>
        </view>
        <input class="form-input" placeholder="例：老年人、晚期癌症患者等" value="{{formData.serviceTarget}}" bindinput="inputChange" data-field="serviceTarget"/>
      </view>
      
      <!-- 服务项目 -->
      <view class="form-item">
        <view class="form-label">
          <text>服务项目</text>
        </view>
        <textarea class="form-textarea" placeholder="请输入提供的服务项目" value="{{formData.serviceItems}}" bindinput="inputChange" data-field="serviceItems"/>
      </view>
      
      <!-- 机构介绍 -->
      <view class="form-item">
        <view class="form-label">
          <text>机构介绍</text>
        </view>
        <textarea class="form-textarea" placeholder="请输入机构介绍" value="{{formData.description}}" bindinput="inputChange" data-field="description"/>
      </view>
    </view>
    
    <!-- 提交区域 -->
    <view class="form-actions">
      <button class="submit-btn" bindtap="submitForm">提交登记</button>
    </view>
  </view>
</view> 