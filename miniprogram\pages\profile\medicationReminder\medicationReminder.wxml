<view class="reminder-container">
  <!-- 顶部标题 -->
  <view class="header">
    <view class="title-container">
      <text class="title">用药提醒</text>
      <view class="title-decoration"></view>
    </view>
  </view>

  <!-- 提醒列表 -->
  <view class="reminder-list">
    <view wx:if="{{reminders.length === 0}}" class="empty-tip">
      <image class="empty-image" src="/images/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无提醒</text>
      <text class="empty-subtext">点击下方按钮添加提醒</text>
    </view>

    <view wx:for="{{reminders}}" 
          wx:key="_id" 
          class="reminder-card">
      <view class="reminder-info">
        <view class="reminder-name">{{item.name}}</view>
        <view class="reminder-time">{{item.time}}</view>
        <view class="reminder-days">
          <text wx:for="{{item.days}}" 
                wx:key="*this" 
                class="day-tag">{{weekDays[item]}}</text>
        </view>
        <view class="reminder-note" wx:if="{{item.note}}">{{item.note}}</view>
      </view>
      <view class="reminder-actions">
        <button class="action-btn edit-btn" bindtap="editReminder" data-id="{{item._id}}">编辑</button>
        <button class="action-btn delete-btn" bindtap="deleteReminder" data-id="{{item._id}}">删除</button>
      </view>
    </view>
  </view>

  <!-- 添加提醒按钮 -->
  <view class="add-btn-container" wx:if="{{!isAdding}}">
    <button class="add-btn" bindtap="addReminder">
      <text class="add-icon">+</text>
      <text>添加提醒</text>
    </button>
  </view>

  <!-- 添加提醒表单 -->
  <view class="add-form" wx:if="{{isAdding}}">
    <view class="form-item">
      <text class="form-label">药品名称</text>
      <input class="form-input" 
             value="{{newReminder.name}}" 
             bindinput="onNameInput" 
             placeholder="请输入药品名称"/>
    </view>

    <view class="form-item">
      <text class="form-label">提醒时间</text>
      <picker mode="time" 
              value="{{newReminder.time}}" 
              bindchange="onTimeChange">
        <view class="time-picker">
          <text>{{newReminder.time || '请选择时间'}}</text>
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="form-label">重复天数</text>
      <view class="days-container">
        <view wx:for="{{weekDays}}" 
              wx:key="*this" 
              class="day-item {{selectedDays.indexOf(index) > -1 ? 'selected' : ''}}"
              bindtap="onDaySelect"
              data-day="{{index}}">
          {{item}}
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="form-label">备注</text>
      <textarea class="form-textarea" 
                value="{{newReminder.note}}" 
                bindinput="onNoteInput" 
                placeholder="请输入备注信息"/>
    </view>

    <view class="form-actions">
      <button class="cancel-btn" bindtap="cancelAdd">取消</button>
      <button class="save-btn" bindtap="saveReminder">保存</button>
    </view>
  </view>
</view> 