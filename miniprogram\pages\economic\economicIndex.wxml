<view class="knowledge-container">
  <!-- 页面标题 -->


  <!-- 管理员添加按钮 -->
  <view class="admin-actions" wx:if="{{userRole === 'admin' || isAdmin}}">
    <button class="add-btn" bindtap="navigateToEdit">
      <text class="add-icon">+</text>
      <text>添加资讯</text>
    </button>
  </view>

  <!-- 文章列表 -->
  <view class="knowledge-list">
    <view wx:if="{{articleList.length === 0 && !loading}}" class="empty-tip">
      <image class="empty-image" src="/images/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无经济支持资讯</text>
      <text class="empty-subtext">我们正在努力更新中...</text>
    </view>

    <view wx:for="{{articleList}}" 
          wx:key="_id" 
          class="article-card"
          bindtap="navigateToDetail"
          data-id="{{item._id}}">
      <view class="card-left">
        <image class="article-image" 
               src="{{item.coverImage || '/images/default-cover.png'}}" 
               mode="aspectFill"></image>
      </view>
      <view class="card-right">
        <view class="article-title">{{item.title}}</view>
        <view class="article-summary">{{item.summary || '暂无摘要'}}</view>
        <view class="article-info">
          <text class="article-author" wx:if="{{item.author}}">{{item.author}}</text>
          <text class="article-time">{{formatTime(item.createTime)}}</text>
        </view>
        <!-- 管理员操作按钮 -->
        <view class="admin-buttons" wx:if="{{userRole === 'admin' || isAdmin}}" catchtap="stopPropagation">
          <button class="icon-btn edit-btn" catchtap="navigateToEdit" data-id="{{item._id}}">✏️</button>
          <button class="icon-btn delete-btn" catchtap="deleteArticle" data-id="{{item._id}}">🗑️</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{loading && articleList.length === 0}}">
    <view class="loading">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 底部加载更多 -->
  <view class="loading-container" wx:if="{{loadingMore}}">
    <view class="loading">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text>加载更多...</text>
  </view>

  <!-- 没有更多了 -->
  <view class="no-more" wx:if="{{!hasMore && articleList.length > 0}}">
    <text>— 没有更多内容了 —</text>
  </view>
</view>

<!-- 加载错误提示 -->
<view class="error-notice" wx:if="{{showError}}">
  <text>{{errorMsg}}</text>
  <button bindtap="handleRetry" class="retry-btn">重试</button>
</view> 