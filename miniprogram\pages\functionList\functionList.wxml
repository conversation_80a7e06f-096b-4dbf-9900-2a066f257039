<view class="container">
  <view class="header">
    <view class="subtitle">为您精心准备的功能：</view>
  </view>

  <view class="function-list-container">
    <block wx:if="{{availableFunctions.length > 0}}">
      <view class="function-card"
            wx:for="{{availableFunctions}}"
            wx:key="key"
            wx:for-index="idx"
            bindtap="navigateToFunction"
            data-key="{{item.key}}"
            data-name="{{item.name}}"
            data-description="{{item.description}}">
        <view class="item-left">
           <view class="icon-background">
              <image src="/images/{{idx+1}}.png" class="item-icon" mode="aspectFit"></image>
           </view>
           <view class="item-text-container">
             <text class="item-text">{{item.name}}</text>
             <text class="item-description">{{item.description}}</text>
           </view>
        </view>
        <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image> 
      </view>
    </block>
    <block wx:else>
      <view class="no-functions">暂无为您开放的功能</view>
    </block>
  </view>

  <view class="footer-tip">安宁疗护，用心陪伴</view>

</view>