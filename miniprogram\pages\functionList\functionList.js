Page({

    /**
     * 页面的初始数据
     */
    data: {
      role: 'unknown', // 当前角色类型
      roleName: '未知角色', // 当前角色名称 (用于显示)
      availableFunctions: [] // 当前角色可用的功能列表
    },
  
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      this.checkRole();
    },
  
    // 检查用户角色并加载相应功能
    checkRole: function() {
      const userRole = wx.getStorageSync('userRole') || 'user';
      
      // 根据角色设置名称
      let roleName = '普通用户';
      switch(userRole) {
        case 'admin':
          roleName = '管理员';
          break;
        case 'socialWorker':
          roleName = '社工';
          break;
        case 'patient':
          roleName = '案主';
          break;
        case 'family':
          roleName = '家属';
          break;
      }
      
      // 定义所有功能列表
      const functionsList = {
        // 所有用户通用功能
        common: [
          {
            key: 'knowledge',
            name: '安宁疗护基础知识',
            icon: 'info',
            description: '了解安宁疗护相关知识'
          },
          {
            key: 'physical',
            name: '生理板块',
            icon: 'medical',
            description: '生理症状管理相关内容'
          },
          {
            key: 'psychology',
            name: '心理板块',
            icon: 'heart',
            description: '心理支持与辅导服务'
          },
          {
            key: 'volunteer',
            name: '志愿者板块',
            icon: 'group',
            description: '志愿者招募与管理'
          },
          {
            key: 'supportGroup',
            name: '互助小组',
            icon: 'people',
            description: '病友互助与经验分享'
          },
          {
            key: 'needAssessment',
            name: '需求评估与服务效果评估',
            icon: 'file',
            description: '开展需求评估与效果评估'
          },
          {
            key: 'education',
            name: '社工继续教育',
            icon: 'book',
            description: '专业知识更新与技能培训'
          },
          {
            key: 'economicSupport',
            name: '经济支持服务',
            icon: 'money',
            description: '经济援助与资源对接'
          },
          {
            key: 'legal',
            name: '法律板块',
            icon: 'scale',
            description: '法律援助与咨询'
          },
          {
            key: 'funeral',
            name: '死亡与丧葬',
            icon: 'flower',
            description: '丧葬事宜与哀伤辅导'
          }
        ],
        
        // 社工特有功能
        socialWorker: [
          
        ],
        
        // 案主与家属共有功能
        patientAndFamily: [
         
        ],
        
        // 管理员特有功能
        admin: [
          // 管理员可以访问所有功能
        ]
      };
      
      // 根据角色设置可用功能
      let availableFunctions = [...functionsList.common];
      
      if (userRole === 'admin') {
        // 管理员可访问所有功能
        availableFunctions = [
          ...functionsList.common,
          ...functionsList.socialWorker,
          ...functionsList.patientAndFamily
        ];
        // 去除重复项
        availableFunctions = this.removeDuplicates(availableFunctions, 'key');
      } else if (userRole === 'socialWorker') {
        // 社工特有功能
        availableFunctions = availableFunctions.concat(functionsList.socialWorker);
      } else if (userRole === 'patient' || userRole === 'family') {
        // 案主和家属共有功能
        availableFunctions = availableFunctions.concat(functionsList.patientAndFamily);
      }
      
      this.setData({
        role: userRole,
        roleName: roleName,
        availableFunctions: availableFunctions
      });
    },
  
    // 去除重复功能项
    removeDuplicates: function(arr, key) {
      return [...new Map(arr.map(item => [item[key], item])).values()];
    },
  
    /**
     * 点击功能模块跳转处理函数
     */
    navigateToFunction: function(e) {
      const key = e.currentTarget.dataset.key;
      const name = e.currentTarget.dataset.name;
      const description = e.currentTarget.dataset.description || this.getFunctionDescription(key);
      
      // 根据功能key跳转到相应页面
      switch(key) {
        case 'knowledge':
          wx.navigateTo({
            url: '/pages/knowledge/knowledgeList'
          });
          break;
        case 'physical':
          wx.navigateTo({
            url: `/pages/physical/physicalIndex`
          });
          break;
        case 'psychology':
          wx.navigateTo({
            url: `/pages/psychology/psychologyIndex`
          });
          break;
        case 'volunteer':
          wx.navigateTo({
            url: `/pages/volunteer/volunteerIndex`
          });
          break;
        case 'supportGroup':
          wx.navigateTo({
            url: `/pages/supportGroup/supportGroupIndex`
          });
          break;
        case 'needAssessment':
          wx.navigateTo({
            url: `/pages/assessment/assessmentIndex`
          });
          break;
        case 'economicSupport':
          wx.navigateTo({
            url: `/pages/economic/economicIndex`
          });
          break;
        case 'legal':
          wx.navigateTo({
            url: `/pages/legal/legalIndex`
          });
          break;
        case 'funeral':
          wx.navigateTo({
            url: `/pages/funeral/funeralIndex`
          });
          break;
        case 'education':
          wx.navigateTo({
            url: `/pages/education/educationIndex`
          });
          break;
        default:
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
      }
    },
    
    // 获取功能项的描述
    getFunctionDescription: function(key) {
      // 查找当前功能项
      const allFunctions = [
        ...this.data.availableFunctions
      ];
      
      const functionItem = allFunctions.find(item => item.key === key);
      return functionItem ? functionItem.description : '';
    },
  
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
  
    },
  
    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
      // 每次显示时检查角色，确保角色变更后列表更新
      this.checkRole();
    },
  
    // ... 其他生命周期函数和事件处理函数 ...
  })
  