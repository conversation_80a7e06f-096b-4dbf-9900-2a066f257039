const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    pageTitle: '加入我们',
    userRole: 'user',
    isEditing: false,
    contentData: {
      image1: '/images/default-volunteer.png', // 默认图片1
      image2: '/images/default-volunteer2.png', // 默认图片2
      text: '欢迎加入我们的志愿者队伍，一起为社会贡献力量！\n\n加入志愿者团队，您将有机会参与关爱老人、社区服务等多种形式的志愿活动，用爱心和行动温暖社会每一个角落。\n\n长按二维码可保存并扫码加入！',
      _id: '', // 用于存储云数据库中的文档ID
      type: 'volunteer' // 类型标识，区分不同模块
    },
    tempContent: {} // 临时存储编辑内容
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    this.setData({
      userRole: userRole
    });
    
    // 从云数据库加载内容
    this.loadContent();
  },

  /**
   * 加载内容
   */
  loadContent: function() {
    const db = wx.cloud.database();
    
    wx.showLoading({
      title: '加载中',
      mask: true
    });
    
    // 查询志愿者招募信息
    db.collection('volunteerJoinUs')
      .where({
        type: 'volunteer' // 只查询志愿者类型的数据
      })
      .limit(1) // 只取一条数据
      .get()
      .then(res => {
        wx.hideLoading();
        
        if (res.data && res.data.length > 0) {
          // 如果有数据，使用数据库中的内容
          this.setData({
            'contentData': res.data[0]
          });
        } else {
          console.log('未找到内容，使用默认内容');
          // 如果没有数据，使用默认内容
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取内容失败:', err);
        
        wx.showToast({
          title: '获取内容失败',
          icon: 'none'
        });
      });
  },

  /**
   * 开始编辑
   */
  startEdit: function() {
    // 复制当前内容到临时变量
    this.setData({
      isEditing: true,
      tempContent: JSON.parse(JSON.stringify(this.data.contentData))
    });
  },

  /**
   * 取消编辑
   */
  cancelEdit: function() {
    this.setData({
      isEditing: false,
      tempContent: {}
    });
  },

  /**
   * 保存编辑
   */
  saveEdit: function() {
    const db = wx.cloud.database();
    
    wx.showLoading({
      title: '保存中',
      mask: true
    });
    
    if (this.data.contentData._id) {
      // 更新现有文档
      db.collection('volunteerJoinUs')
        .doc(this.data.contentData._id)
        .update({
          data: {
            image1: this.data.tempContent.image1,
            image2: this.data.tempContent.image2,
            text: this.data.tempContent.text,
            type: 'volunteer',
            updateTime: db.serverDate()
          }
        })
        .then(() => {
          this.handleSaveSuccess();
        })
        .catch(err => {
          this.handleSaveError(err);
        });
    } else {
      // 创建新文档
      db.collection('volunteerJoinUs')
        .add({
          data: {
            image1: this.data.tempContent.image1,
            image2: this.data.tempContent.image2,
            text: this.data.tempContent.text,
            type: 'volunteer',
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
        })
        .then(res => {
          this.setData({
            'contentData._id': res._id
          });
          this.handleSaveSuccess();
        })
        .catch(err => {
          this.handleSaveError(err);
        });
    }
  },

  /**
   * 保存成功处理
   */
  handleSaveSuccess: function() {
    wx.hideLoading();
    
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 更新内容
    this.setData({
      'contentData.image1': this.data.tempContent.image1,
      'contentData.image2': this.data.tempContent.image2,
      'contentData.text': this.data.tempContent.text,
      isEditing: false,
      tempContent: {}
    });
  },

  /**
   * 保存失败处理
   */
  handleSaveError: function(err) {
    wx.hideLoading();
    console.error('保存内容失败:', err);
    
    wx.showToast({
      title: '保存失败',
      icon: 'none'
    });
  },

  /**
   * 文本输入事件处理
   */
  onTextInput: function(e) {
    this.setData({
      'tempContent.text': e.detail.value
    });
  },

  /**
   * 选择图片1
   */
  chooseImage1: function() {
    wx.chooseImage({
      count: 1, // 最多可以选择的图片张数
      sizeType: ['compressed'], // 压缩图
      sourceType: ['album', 'camera'], // 相册和相机
      success: res => {
        // 上传图片到云存储
        this.uploadImage(res.tempFilePaths[0], 'image1');
      }
    });
  },

  /**
   * 选择图片2
   */
  chooseImage2: function() {
    wx.chooseImage({
      count: 1, // 最多可以选择的图片张数
      sizeType: ['compressed'], // 压缩图
      sourceType: ['album', 'camera'], // 相册和相机
      success: res => {
        // 上传图片到云存储
        this.uploadImage(res.tempFilePaths[0], 'image2');
      }
    });
  },

  /**
   * 上传图片到云存储
   */
  uploadImage: function(tempFilePath, imageField) {
    wx.showLoading({
      title: '上传中',
      mask: true
    });
    
    const fileName = `volunteer_${imageField}_${Date.now()}${Math.floor(Math.random() * 1000)}.${tempFilePath.match(/\.([^\.]+)$/)[1]}`;
    
    wx.cloud.uploadFile({
      cloudPath: `volunteer/${fileName}`, // 云端路径
      filePath: tempFilePath, // 本地文件路径
      success: res => {
        wx.hideLoading();
        
        // 获取图片的云存储路径
        const fileID = res.fileID;
        
        // 更新临时内容
        this.setData({
          [`tempContent.${imageField}`]: fileID
        });
        
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('上传图片失败:', err);
        
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 保存图片到本地
   */
  saveImageToAlbum: function(e) {
    const imageUrl = e.currentTarget.dataset.url;
    
    // 获取图片信息，判断是本地路径还是网络路径
    if (imageUrl.startsWith('http') || imageUrl.startsWith('cloud://')) {
      // 如果是网络图片或云存储图片，先下载
      wx.showLoading({
        title: '保存中',
        mask: true
      });
      
      wx.cloud.downloadFile({
        fileID: imageUrl,
        success: res => {
          const tempFilePath = res.tempFilePath;
          this.saveToAlbum(tempFilePath);
        },
        fail: err => {
          wx.hideLoading();
          console.error('下载图片失败:', err);
          
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 本地图片直接保存
      this.saveToAlbum(imageUrl);
    }
  },

  /**
   * 保存图片到相册
   */
  saveToAlbum: function(filePath) {
    wx.saveImageToPhotosAlbum({
      filePath: filePath,
      success: () => {
        wx.hideLoading();
        
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('保存图片到相册失败:', err);
        
        if (err.errMsg.indexOf('auth deny') !== -1) {
          wx.showModal({
            title: '提示',
            content: '需要您授权保存图片到相册',
            confirmText: '去授权',
            success: res => {
              if (res.confirm) {
                wx.openSetting({
                  success: settingRes => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      wx.showToast({
                        title: '授权成功，请重新保存',
                        icon: 'none'
                      });
                    } else {
                      wx.showToast({
                        title: '授权失败',
                        icon: 'none'
                      });
                    }
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: this.data.pageTitle,
      path: '/pages/volunteer/joinUs/joinUs'
    }
  }
}) 