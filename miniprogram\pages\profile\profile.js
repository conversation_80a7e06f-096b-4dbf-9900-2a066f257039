// 获取应用实例
const app = getApp();

Page({
  data: {
    userInfo: null,
    isLogin: false,
    openid: null,
    isLoading: false,
    userRole: 'user' // 新增：用户权限，默认为普通用户
  },

  onLoad: function () {
    // 检查用户登录状态
    this.checkLoginStatus();
    
    // 忽略getSystemInfoSync弃用警告 - 这是微信自己的组件使用的，不是我们的代码调用的
    console.warn = function() {};
  },

  onShow: function () {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus: function () {
    // 从缓存或全局数据中获取用户信息
    let userInfo = app.globalData.userInfo;
    let openid = wx.getStorageSync('openid');
    let userRole = wx.getStorageSync('userRole') || 'user';
    
    if (!userInfo) {
      userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        app.globalData.userInfo = userInfo;
      }
    }

    if (userInfo && openid) {
      // 已登录
      this.setData({
        isLogin: true,
        userInfo: userInfo,
        openid: openid,
        userRole: userRole
      });
      
      // 检查数据库中的最新用户信息
      this.refreshUserInfoFromDB(openid);
    } else {
      // 未登录
      this.setData({
        isLogin: false,
        userInfo: null,
        openid: null,
        userRole: 'user'
      });
    }
  },

  // 从数据库刷新用户信息
  refreshUserInfoFromDB: function(openid) {
    const db = wx.cloud.database();
    
    db.collection('users').where({
      _openid: openid
    }).get().then(res => {
      if (res.data.length > 0) {
        const dbUserInfo = res.data[0];
        
        // 获取当前存储的用户信息
        let userInfo = this.data.userInfo || {};
        
        // 使用数据库中的信息，保留openid
        let mergedInfo = {
          ...dbUserInfo,
          openid: openid
        };
        
        // 更新全局和缓存
        app.globalData.userInfo = mergedInfo;
        wx.setStorageSync('userInfo', mergedInfo);
        
        // 确保用户角色
        const userRole = dbUserInfo.userRole || 'user';
        wx.setStorageSync('userRole', userRole);
        
        // 更新页面数据
        this.setData({
          userInfo: mergedInfo,
          userRole: userRole
        });
      }
    }).catch(err => {
      console.error('获取数据库用户信息失败:', err);
    });
  },

  // 获取用户信息
  onGetUserInfo: function (e) {
    if (e.detail.userInfo) {
      // 用户授权，获取信息成功
      const wxUserInfo = e.detail.userInfo;
      this.handleWechatLogin(wxUserInfo);
                  } else {
      // 用户拒绝授权
      wx.showToast({
        title: '需要您的授权才能登录',
        icon: 'none'
      });
    }
  },
  
  // 处理登录失败
  handleLoginFail: function (errMsg) {
    this.setData({ isLoading: false });
                  wx.hideLoading();
    wx.showToast({
      title: errMsg,
      icon: 'none'
    });
  },
  
  // 从数据库获取用户完整信息
  fetchUserInfoFromDB: function(openid, wxUserInfo) {
    const db = wx.cloud.database();
    
    return new Promise((resolve, reject) => {
      db.collection('users').where({
        _openid: openid
      }).get().then(res => {
        if (res.data.length > 0) {
          const dbUserInfo = res.data[0];
          const userRole = dbUserInfo.userRole || 'user';
          
          // 只更新登录时间，不覆盖头像和昵称
          db.collection('users').doc(dbUserInfo._id).update({
            data: {
              lastLoginTime: db.serverDate()
            }
          }).then(() => {
            console.log('用户登录时间已更新');
          }).catch(err => {
            console.error('更新用户登录时间失败:', err);
          });
          
          // 只返回用户角色信息，界面更新由调用方处理
          resolve(userRole);
        } else {
          // 如果不存在，创建新用户
          this.createNewUser(wxUserInfo).then(() => {
            // 返回默认角色
            resolve('user');
          }).catch(err => {
            console.error('创建用户失败:', err);
            reject(err);
          });
        }
      }).catch(err => {
        console.error('查询用户信息失败:', err);
        reject(err);
      });
    });
  },

  // 创建新用户
  createNewUser: function(userInfo) {
    const db = wx.cloud.database();
    
    return new Promise((resolve, reject) => {
      db.collection('users').add({
        data: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          gender: userInfo.gender || 0, // 确保性别字段存在
          country: userInfo.country,
          province: userInfo.province,
          city: userInfo.city,
          userRole: 'user', // 默认角色
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      }).then(res => {
        console.log('新用户创建成功');
        resolve(res);
      }).catch(err => {
        console.error('创建新用户失败:', err);
        reject(err);
      });
    });
  },
  
  // 保存用户信息到数据库
  saveUserInfo: function (userInfo) {
    const db = wx.cloud.database();
    // 检查是否存在该用户记录
    db.collection('users').where({
      _openid: userInfo.openid
    }).get().then(res => {
      if (res.data.length === 0) {
        // 不存在，创建新记录
        db.collection('users').add({
          data: {
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            gender: userInfo.gender,
            country: userInfo.country,
            province: userInfo.province,
            city: userInfo.city,
            userRole: userInfo.userRole || 'user', // 添加用户角色
            createTime: db.serverDate(),
            updateTime: db.serverDate()
          }
        }).then(() => {
          console.log('用户信息保存成功');
        }).catch(err => {
          console.error('保存用户信息失败:', err);
        });
      } else {
        // 存在，更新记录
        db.collection('users').doc(res.data[0]._id).update({
          data: {
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            userRole: userInfo.userRole || 'user', // 确保保存用户角色
            updateTime: db.serverDate()
          }
        }).then(() => {
          console.log('用户信息更新成功');
        }).catch(err => {
          console.error('更新用户信息失败:', err);
        });
      }
    }).catch(err => {
      console.error('查询用户失败:', err);
    });
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除全局数据和存储
          app.globalData.userInfo = null;
          wx.removeStorageSync('userInfo');
          // 保留openid以便重新登录
          // wx.removeStorageSync('openid');
          wx.removeStorageSync('userRole');
          
          // 如果有其它相关缓存也清除
          // ...
          
          // 更新页面状态
          this.setData({
            isLogin: false,
            userInfo: null,
            userRole: 'user'
          });
          
          // 重新加载页面以确保完全刷新
          setTimeout(() => {
            this.checkLoginStatus();
          }, 200);
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },
  
  // 修改头像
  changeAvatar: function () {
    if (!this.data.isLogin) {
      this.showLoginTip();
      return;
    }
    
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        let sourceType = ['album', 'camera'];
        
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: [sourceType[res.tapIndex]],
          sizeType: ['compressed'], // 使用压缩图片，减少上传失败概率
          success: (res) => {
            let tempFilePath = res.tempFiles[0].tempFilePath;
            
            wx.showLoading({
              title: '上传中',
            });
            
            // 压缩图片
            wx.compressImage({
              src: tempFilePath,
              quality: 80, // 压缩质量
              success: (compressRes) => {
                console.log('图片压缩成功:', compressRes);
                // 上传压缩后的图片
                this.uploadImageWithRetry(compressRes.tempFilePath, 0);
              },
              fail: (err) => {
                console.log('图片压缩失败，使用原图上传:', err);
                // 压缩失败则使用原图
                this.uploadImageWithRetry(tempFilePath, 0);
              }
            });
          }
        });
      }
    });
  },
  
  // 带重试机制的图片上传
  uploadImageWithRetry: function(filePath, retryCount) {
    const maxRetries = 3; // 最大重试次数
    
    // 上传图片到云存储
    const cloudPath = `avatar/${this.data.openid}_${new Date().getTime()}.${filePath.substr(filePath.lastIndexOf('.') + 1)}`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: (res) => {
        // 更新用户头像
        let userInfo = this.data.userInfo;
        userInfo.avatarUrl = res.fileID;
        
        // 更新本地用户信息
        app.globalData.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);
        
        // 更新页面显示
        this.setData({
          userInfo: userInfo
        });
        
        // 更新数据库中的用户信息
        this.updateUserInfoField('avatarUrl', res.fileID);
        
        wx.hideLoading();
        wx.showToast({
          title: '头像已更新',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error(`上传头像失败(尝试${retryCount+1}/${maxRetries+1}):`, err);
        
        if (retryCount < maxRetries) {
          // 还有重试机会，延迟后重试
          wx.showToast({
            title: `上传失败，正在重试(${retryCount+1}/${maxRetries})`,
            icon: 'none',
            duration: 1000
          });
          
          setTimeout(() => {
            this.uploadImageWithRetry(filePath, retryCount + 1);
          }, 1000); // 延迟1秒后重试
        } else {
          // 已达到最大重试次数
          wx.hideLoading();
          wx.showToast({
            title: '上传失败，请检查网络',
            icon: 'none'
          });
        }
      }
    });
  },
  
  // 修改昵称
  changeNickname: function () {
    if (!this.data.isLogin) {
      this.showLoginTip();
      return;
    }
    
    wx.showModal({
      title: '修改昵称',
      content: '',
      editable: true,
      placeholderText: '请输入新昵称',
      success: (res) => {
        if (res.confirm && res.content) {
          let newNickname = res.content.trim();
          if (newNickname) {
            let userInfo = this.data.userInfo;
            userInfo.nickName = newNickname;
            
            // 更新本地用户信息
            app.globalData.userInfo = userInfo;
            wx.setStorageSync('userInfo', userInfo);
            
            // 更新页面显示
            this.setData({
              userInfo: userInfo
            });
            
            // 更新数据库中的用户信息
            this.updateUserInfoField('nickName', newNickname);
            
            wx.showToast({
              title: '昵称已更新',
              icon: 'success'
            });
          }
        }
      }
    });
  },
  
  // 更新数据库中的用户信息字段
  updateUserInfoField: function (field, value) {
    const db = wx.cloud.database();
    
    db.collection('users').where({
      _openid: this.data.openid
    }).get().then(res => {
      if (res.data.length > 0) {
        let updateData = {};
        updateData[field] = value;
        updateData.updateTime = db.serverDate();
        
        db.collection('users').doc(res.data[0]._id).update({
          data: updateData
        }).then(() => {
          console.log('用户信息字段更新成功');
        }).catch(err => {
          console.error('更新用户信息字段失败:', err);
        });
      }
    }).catch(err => {
      console.error('查询用户失败:', err);
    });
  },
  
  // 显示登录提示
  showLoginTip: function () {
    wx.showModal({
      title: '提示',
      content: '请先登录后再使用此功能',
      showCancel: false
    });
  },
  
  // 功能列表点击事件
  navigateToFavorites: function () {
    if (!this.data.isLogin) {
      this.showLoginTip();
      return;
    }
    
    wx.showToast({
      title: '我的收藏功能开发中',
      icon: 'none'
    });
    
    // 取消下方注释以实现页面跳转
    // wx.navigateTo({
    //   url: '/pages/favorites/favorites'
    // });
  },
  
  navigateToPosts: function () {
    if (!this.data.isLogin) {
      this.showLoginTip();
      return;
    }
    
    wx.showToast({
      title: '我的发帖功能开发中',
      icon: 'none'
    });
    
    // 取消下方注释以实现页面跳转
    // wx.navigateTo({
    //   url: '/pages/myPosts/myPosts'
    // });
  },
  
  
  navigateToReminders: function () {
    if (!this.data.isLogin) {
      this.showLoginTip();
      return;
    }
    
    wx.showToast({
      title: '用药提醒功能开发中',
      icon: 'none'
    });
  },
    //   wx.navigateTo({
  //     url: '/pages/profile/medicationReminder/medicationReminder'
  //   });
  // },
  // 管理员功能导航
  navigateToAdmin: function () {
    if (!this.data.isLogin) {
      this.showLoginTip();
      return;
    }
    
    // 检查用户是否有管理员权限
    if (this.data.userRole !== 'admin') {
      wx.showToast({
        title: '权限不足',
        icon: 'none'
      });
      return;
    }
    
    wx.showToast({
      title: '管理员功能开发中',
      icon: 'none'
    });
    
    // 管理员页面跳转
    // wx.navigateTo({
    //   url: '/pages/admin/admin'
    // });
  },
  
  // 导航到我的信息页面
  navigateToMyInfo: function () {
    if (!this.data.isLogin) {
      this.showLoginTip();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/myInfo/myInfo?openid=' + this.data.openid
    });
  },

  // 处理微信登录
  handleWechatLogin: function (wxUserInfo) {
    wx.showLoading({
      title: '登录中',
      mask: true
    });
    
    // 获取openid
    const openid = wx.getStorageSync('openid');
    
    if (openid) {
      // 使用现有openid
      this.fetchUserInfoFromDB(openid, wxUserInfo).then(userRole => {
        // 获取数据库中的用户信息
        const db = wx.cloud.database();
        
        return db.collection('users').where({
          _openid: openid
        }).get().then(res => {
          if (res.data.length > 0) {
            const dbUserInfo = res.data[0];
            
            // 优先使用数据库中的头像和昵称
            wxUserInfo.avatarUrl = dbUserInfo.avatarUrl || wxUserInfo.avatarUrl;
            wxUserInfo.nickName = dbUserInfo.nickName || wxUserInfo.nickName;
            wxUserInfo.gender = dbUserInfo.gender !== undefined ? dbUserInfo.gender : wxUserInfo.gender;
            
            // 保存最终用户信息
            wxUserInfo.openid = openid;
            wxUserInfo.userRole = userRole;
            
            // 更新页面显示
            this.setData({
              isLogin: true,
              userInfo: wxUserInfo,
              openid: openid,
              isLoading: false,
              userRole: userRole
            });
            
            // 更新全局和缓存
            app.globalData.userInfo = wxUserInfo;
            wx.setStorageSync('userInfo', wxUserInfo);
            
            wx.hideLoading();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            return Promise.resolve();
          } else {
            // 保存最终用户信息
            wxUserInfo.openid = openid;
            wxUserInfo.userRole = userRole;
            
            // 更新页面显示
            this.setData({
              isLogin: true,
              userInfo: wxUserInfo,
              openid: openid,
              isLoading: false,
              userRole: userRole
            });
            
            wx.hideLoading();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            return Promise.resolve();
          }
        });
      }).catch(err => {
        wx.hideLoading();
        console.error('登录失败:', err);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      });
    } else {
      // 需要获取新的openid
      wx.cloud.callFunction({
        name: 'login',
        data: {}
      }).then(res => {
        const openid = res.result.openid;
        wx.setStorageSync('openid', openid);
        
        this.fetchUserInfoFromDB(openid, wxUserInfo).then(userRole => {
          // 获取数据库中的用户信息
          const db = wx.cloud.database();
          
          return db.collection('users').where({
            _openid: openid
          }).get().then(res => {
            if (res.data.length > 0) {
              const dbUserInfo = res.data[0];
              
              // 优先使用数据库中的头像和昵称
              wxUserInfo.avatarUrl = dbUserInfo.avatarUrl || wxUserInfo.avatarUrl;
              wxUserInfo.nickName = dbUserInfo.nickName || wxUserInfo.nickName;
              wxUserInfo.gender = dbUserInfo.gender !== undefined ? dbUserInfo.gender : wxUserInfo.gender;
            }
            
            // 保存最终用户信息
            wxUserInfo.openid = openid;
            wxUserInfo.userRole = userRole;
            
            // 更新页面显示
            this.setData({
              isLogin: true,
              userInfo: wxUserInfo,
              openid: openid,
              isLoading: false,
              userRole: userRole
            });
            
            // 更新全局和缓存
            app.globalData.userInfo = wxUserInfo;
            wx.setStorageSync('userInfo', wxUserInfo);
            
            wx.hideLoading();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            return Promise.resolve();
          });
        }).catch(err => {
          wx.hideLoading();
          console.error('登录失败:', err);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        });
      }).catch(err => {
        wx.hideLoading();
        console.error('获取openid失败:', err);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      });
    }
  },

  // 跳转到机构登记页面
  navigateToInstitutionRegister: function() {
    if (this.data.userRole !== 'admin') {
      wx.showToast({
        title: '无权限访问',
        icon: 'none'
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/physical/institutionEdit'
    });
  },
}); 