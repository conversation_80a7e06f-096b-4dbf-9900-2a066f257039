// 需求评估与服务效果评估
const app = getApp();
const db = wx.cloud.database();
const _ = db.command;

Page({
  /**
   * 页面的初始数据
   */
  data: {
    pageTitle: '需求评估与服务效果评估',
    pageDescription: '专业评估工具与方法，服务效果评价',
    userRole: 'user', // 默认为普通用户
    articleList: [], // 文章列表
    isLoading: false, // 是否正在加载数据
    hasMore: true, // 是否有更多数据
    pageSize: 10, // 每页获取的文章数量
    skip: 0, // 分页起始位置
    category: 'assessment' // 文章类别标识
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    this.setData({
      userRole: userRole
    });
    
    // 加载文章列表
    this.loadArticles();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次进入页面时重新获取文章列表
    this.setData({
      articleList: [],
      skip: 0,
      hasMore: true
    });
    this.loadArticles();
  },

  // 加载文章列表
  loadArticles: function () {
    if (this.data.isLoading || !this.data.hasMore) return;
    
    this.setData({
      isLoading: true
    });
    
    // 查询文章列表，按创建时间降序排列，并且只显示assessment类别的文章
    db.collection('knowledge')
      .where({
        category: this.data.category // 只查询assessment类别的文章
      })
      .orderBy('createTime', 'desc')
      .skip(this.data.skip)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        // 格式化时间
        const articles = res.data.map(item => {
          // 格式化时间为：YYYY-MM-DD
          let date = new Date(item.createTime);
          item.createTime = date.getFullYear() + '-' + 
                          ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
                          ('0' + date.getDate()).slice(-2);
          
          // 确保有摘要
          if (!item.summary) {
            // 截取内容前100个字符作为摘要
            item.summary = item.content ? item.content.replace(/<[^>]+>/g, '').substring(0, 100) : '暂无摘要';
          }
          
          return item;
        });
        
        // 更新数据
        this.setData({
          articleList: [...this.data.articleList, ...articles],
          isLoading: false,
          skip: this.data.skip + articles.length,
          hasMore: articles.length === this.data.pageSize
        });
      })
      .catch(err => {
        console.error('获取文章列表失败:', err);
        this.setData({
          isLoading: false
        });
        
        wx.showToast({
          title: '获取文章失败',
          icon: 'none'
        });
      });
  },

  // 加载更多
  loadMore: function () {
    this.loadArticles();
  },

  // 跳转到文章详情页
  navigateToDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/knowledge/knowledgeDetail?id=${id}&category=${this.data.category}`
    });
  },

  // 跳转到编辑页面（新增文章）
  navigateToEdit: function (e) {
    // 检查是否有ID
    const id = e.currentTarget.dataset ? e.currentTarget.dataset.id : '';
    
    if (id) {
      // 编辑现有文章
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeEdit?id=${id}&category=${this.data.category}`
      });
    } else {
      // 新增文章
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeEdit?category=${this.data.category}`
      });
    }
  },

  // 删除文章
  deleteArticle: function (e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除此文章吗？',
      success: res => {
        if (res.confirm) {
          const db = wx.cloud.database();
          
          wx.showLoading({
            title: '正在删除',
            mask: true
          });
          
          db.collection('knowledge').doc(id).remove().then(() => {
            wx.hideLoading();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 更新文章列表
            this.setData({
              articleList: this.data.articleList.filter(item => item._id !== id)
            });
          }).catch(err => {
            wx.hideLoading();
            console.error('删除文章失败:', err);
            
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 阻止事件冒泡
  stopPropagation: function (e) {
    return false;
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: this.data.pageTitle,
      path: '/pages/assessment/assessmentIndex'
    }
  }
}); 