/* pages/knowledge/knowledgeList.wxss */
.knowledge-container {
  min-height: 100vh;
  background: linear-gradient(to bottom, #fff8f0, #fcf1e7);
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 头部标题区域 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.title-container {
  position: relative;
  margin-bottom: 16rpx;
}

.title {
  font-size: 42rpx;
  font-weight: bold;
  color: #6a5c4c;
  position: relative;
  z-index: 1;
}

.title-decoration {
  position: absolute;
  bottom: -6rpx;
  left: -8rpx;
  right: -8rpx;
  height: 16rpx;
  background: rgba(255, 184, 140, 0.3);
  border-radius: 8rpx;
  z-index: 0;
}

.subtitle {
  font-size: 28rpx;
  color: #9c8e7f;
  margin-top: 10rpx;
}

/* 管理员添加按钮 */
.admin-actions {
  margin-bottom: 30rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background: linear-gradient(135deg, #ffb88c, #ffa372);
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 163, 114, 0.25);
  transition: all 0.3s ease;
}

.add-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 163, 114, 0.2);
}

.add-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
  line-height: 30rpx;
}

/* 文章列表 */
.knowledge-list {
  width: 100%;
}

/* 空状态 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #9c8e7f;
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 24rpx;
  color: #b3a090;
}

/* 文章卡片 */
.article-card {
  display: flex;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(211, 187, 164, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.article-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(211, 187, 164, 0.1);
}

.card-left {
  width: 200rpx;
  height: 200rpx;
  margin-right: 24rpx;
  overflow: hidden;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.article-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  transition: transform 0.6s ease;
}

.article-card:active .article-image {
  transform: scale(1.05);
}

.card-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.article-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #6a5c4c;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.article-summary {
  font-size: 28rpx;
  color: #9c8e7f;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.article-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.article-author {
  color: #ffb88c;
  margin-right: 16rpx;
}

.article-time {
  color: #b3a090;
}

/* 管理员操作按钮 */
.admin-buttons {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
}

.icon-btn {
  width: 60rpx;
  height: 60rpx;
  padding: 0;
  margin-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  font-size: 32rpx;
  line-height: 1;
}

.icon-btn::after {
  border: none;
}

/* 加载更多 */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.loading {
  display: flex;
  align-items: center;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ffb88c;
  margin: 0 8rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.load-more, .no-more {
  font-size: 26rpx;
  color: #9c8e7f;
}

.load-more {
  padding: 10rpx 40rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.6);
}

.no-more {
  opacity: 0.6;
}

/* 优化顶部tab菜单样式 */
.tab-container {
  display: flex;
  background: linear-gradient(to right, #fff8f0, #fcf1e7);
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(211, 187, 164, 0.15);
  position: relative;
  overflow: hidden;
}

.tab-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5rpx);
  z-index: 0;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.tab text {
  font-size: 32rpx;
  color: #9c8e7f;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
}

.tab.active text {
  color: #ffb88c;
  font-weight: 600;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  border-radius: 4rpx;
  background: linear-gradient(to right, #ffb88c, #ffa372);
  box-shadow: 0 2rpx 4rpx rgba(255, 184, 140, 0.3);
}

/* 许愿池样式 */
.wishpool-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(211, 187, 164, 0.15);
}

.coming-soon-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.7;
}

.coming-soon-text {
  font-size: 30rpx;
  color: #b3a090;
  font-style: italic;
} 