.reminder-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
  text-align: center;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15rpx;
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.title-decoration {
  width: 80rpx;
  height: 6rpx;
  background-color: #4CAF50;
  border-radius: 3rpx;
}

/* 提醒列表 */
.reminder-list {
  margin-bottom: 30rpx;
}

.reminder-card {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.reminder-info {
  margin-bottom: 20rpx;
}

.reminder-name {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.reminder-time {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.reminder-days {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 10rpx;
}

.day-tag {
  background-color: #E8F5E9;
  color: #4CAF50;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.reminder-note {
  font-size: 26rpx;
  color: #999999;
}

.reminder-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  background: none;
  border: 2rpx solid;
}

.edit-btn {
  color: #4CAF50;
  border-color: #4CAF50;
}

.delete-btn {
  color: #F44336;
  border-color: #F44336;
}

/* 空状态提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 220rpx;
  height: 220rpx;
  margin-bottom: 25rpx;
}

.empty-text {
  font-size: 34rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #999999;
}

/* 添加按钮 */
.add-btn-container {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4CAF50;
  color: #ffffff;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.add-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

/* 添加表单 */
.add-form {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.time-picker {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  color: #666666;
}

.days-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.day-item {
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
}

.day-item.selected {
  background-color: #4CAF50;
  color: #ffffff;
  border-color: #4CAF50;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.cancel-btn, .save-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.cancel-btn {
  background-color: #E0E0E0;
  color: #666666;
}

.save-btn {
  background-color: #4CAF50;
  color: #ffffff;
} 