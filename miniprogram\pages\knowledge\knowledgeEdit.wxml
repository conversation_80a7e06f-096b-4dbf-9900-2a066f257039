<view class="edit-container">
  <!-- 顶部操作栏 -->
  <view class="top-bar">
    <view class="back-button" bindtap="navigateBack">
      <text class="back-icon">◀</text>
      <text class="back-text">返回</text>
    </view>
    
    <view class="actions">
      <button class="action-btn save" bindtap="saveArticle" loading="{{isSaving}}">保存</button>
    </view>
  </view>

  <!-- 编辑表单 -->
  <view class="edit-form">
    <!-- 标题 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>标题</text>
      </view>
      <input class="form-input" 
             value="{{article.title}}" 
             placeholder="请输入文章标题"
             bindinput="onTitleInput" />
    </view>
    
    <!-- 作者 -->
    <view class="form-item">
      <view class="form-label">
        <text>作者</text>
      </view>
      <input class="form-input" 
             value="{{article.author}}" 
             placeholder="请输入作者名称"
             bindinput="onAuthorInput" />
    </view>
    
    <!-- 封面图片 -->
    <view class="form-item">
      <view class="form-label">
        <text>封面图片</text>
      </view>
      <view class="upload-wrapper">
        <view class="cover-preview" wx:if="{{article.coverImage || tempCoverPath}}">
          <image class="cover-image" 
                 src="{{tempCoverPath || article.coverImage}}" 
                 mode="aspectFill"></image>
          <view class="cover-actions">
            <view class="cover-action-btn" bindtap="chooseImage">更换</view>
            <view class="cover-action-btn delete" bindtap="removeCover">删除</view>
          </view>
        </view>
        <view class="upload-button" wx:else bindtap="chooseImage">
          <text class="upload-icon">+</text>
          <text class="upload-text">上传封面图片</text>
        </view>
      </view>
    </view>
    
    <!-- 摘要 -->
    <view class="form-item">
      <view class="form-label">
        <text>摘要</text>
      </view>
      <textarea class="form-textarea" 
                value="{{article.summary}}" 
                placeholder="请输入文章摘要"
                bindinput="onSummaryInput"
                maxlength="200" />
      <text class="textarea-counter">{{article.summary.length || 0}}/200</text>
    </view>
    
    <!-- 文章内容 -->
    <view class="form-item content-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>内容</text>
      </view>
      
      <!-- 编辑/预览切换 -->
      <view class="edit-mode-switch">
        <view class="mode-btn {{editMode === 'code' ? 'active' : ''}}" bindtap="switchEditMode" data-mode="code">编辑模式</view>
        <view class="mode-btn {{editMode === 'preview' ? 'active' : ''}}" bindtap="switchEditMode" data-mode="preview">预览模式</view>
      </view>

      <!-- 只在编辑模式显示工具栏 -->
      <view class="editor-toolbar" wx:if="{{editMode === 'code'}}">
        <view class="toolbar-group">
          <view class="toolbar-btn" bindtap="setHeading" data-level="1">H1</view>
          <view class="toolbar-btn" bindtap="setHeading" data-level="2">H2</view>
          <view class="toolbar-btn" bindtap="setHeading" data-level="3">H3</view>
        </view>
        <view class="toolbar-group">
          <view class="toolbar-btn" bindtap="setBold">B</view>
          <view class="toolbar-btn" bindtap="setItalic">I</view>
          <view class="toolbar-btn" bindtap="setUnderline">U</view>
        </view>
        <view class="toolbar-group">
          <view class="toolbar-btn color-btn" bindtap="setColor" data-color="#FF0000" style="color: #FF0000;">红</view>
          <view class="toolbar-btn color-btn" bindtap="setColor" data-color="#0000FF" style="color: #0000FF;">蓝</view>
          <view class="toolbar-btn color-btn" bindtap="setColor" data-color="#008000" style="color: #008000;">绿</view>
        </view>
        <view class="toolbar-group">
          <view class="toolbar-btn" bindtap="insertImage">图</view>
          <view class="toolbar-btn" bindtap="insertList">列</view>
        </view>
      </view>
      
      <!-- 编辑区 -->
      <block wx:if="{{editMode === 'code'}}">
        <textarea class="content-textarea" 
                value="{{article.content}}" 
                placeholder="请输入文章内容，支持HTML标签"
                bindinput="onContentInput"
                auto-height
                maxlength="-1"
                cursor-spacing="80" 
                show-confirm-bar="{{false}}" 
                hold-keyboard />
        
    
      </block>
      
      <!-- 预览区 -->
      <view wx:else class="content-preview">
        <rich-text nodes="{{article.content}}" space="nbsp"></rich-text>
      </view>
    </view>
  </view>
  
  <!-- 底部操作栏 -->

</view>