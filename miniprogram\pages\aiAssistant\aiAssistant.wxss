.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.chat-list {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-list {
  display: flex;
  flex-direction: column;
}

.message-item {
  max-width: 80%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
  word-break: break-all;
}

.user-message {
  align-self: flex-end;
  background-color: #4CAF50;
  color: #ffffff;
}

.ai-message {
  align-self: flex-start;
  background-color: #ffffff;
  color: #333333;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.5;
}

.input-area {
  display: flex;
  padding: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e0e0e0;
}

.message-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 40rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 160rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #4CAF50;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.send-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}

.clear-btn {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  padding: 10rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 30rpx;
  font-size: 24rpx;
} 