const app = getApp();
const db = wx.cloud.database();
const _ = db.command;

Page({
  /**
   * 页面的初始数据
   */
  data: {
    pageTitle: '经济支持服务',
    pageDescription: '帮助用户获取资金支持与政策信息',
    articleList: [],
    page: 0,
    pageSize: 10,
    totalCount: 0,
    hasMore: true,
    loading: true,
    loadingMore: false,
    showError: false,
    errorMsg: '',
    userRole: 'user',
    isAdmin: false,
    category: 'economic'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    // 检查用户角色，只有社工、案主、家属和管理员可以访问
    const allowedRoles = ['socialWorker', 'patient', 'family', 'admin'];
    
    if (!allowedRoles.includes(userRole)) {
      wx.showToast({
        title: '无权访问此功能',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      userRole: userRole
    });
    
    this.checkAdminStatus();
    this.loadArticles();
  },

  /**
   * 检查用户是否有管理员权限
   */
  checkAdminStatus: function () {
    const userInfo = app.globalData.userInfo || {};
    
    // 检查用户是否为管理员
    wx.cloud.callFunction({
      name: 'checkAdmin',
      data: {},
      success: res => {
        this.setData({
          isAdmin: res.result && res.result.isAdmin || false
        });
      },
      fail: err => {
        console.error('检查管理员状态失败', err);
        this.setData({
          isAdmin: false
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次进入页面时重新获取文章列表
    this.setData({
      articleList: [],
      page: 0,
      hasMore: true,
      loading: true,
      showError: false
    });
    this.loadArticles();
  },

  /**
   * 加载文章列表
   */
  loadArticles: function () {
    if (this.data.loading && this.data.page > 0) {
      this.setData({ loadingMore: true });
    }
    
    if (this.data.loadingMore || !this.data.hasMore) return;
    
    this.setData({ loading: true, showError: false });
    
    // 使用知识系统的数据库方式
    db.collection('knowledge')
      .where({
        category: this.data.category // 只查询economic类别的文章
      })
      .orderBy('createTime', 'desc')
      .skip(this.data.page * this.data.pageSize)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        // 格式化时间
        const articles = res.data.map(item => {
          // 格式化时间为：YYYY-MM-DD
          let date = new Date(item.createTime);
          item.createTime = date.getFullYear() + '-' + 
                          ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
                          ('0' + date.getDate()).slice(-2);
          
          // 确保有摘要
          if (!item.summary) {
            // 截取内容前100个字符作为摘要
            item.summary = item.content ? item.content.replace(/<[^>]+>/g, '').substring(0, 100) : '暂无摘要';
          }
          
          return item;
        });
        
        // 更新数据
        this.setData({
          loading: false,
          loadingMore: false,
          articleList: [...this.data.articleList, ...articles],
          page: this.data.page + 1,
          hasMore: articles.length === this.data.pageSize
        });
        
        // 获取总条数
        if (this.data.page === 1) {
          db.collection('knowledge')
            .where({ category: this.data.category })
            .count()
            .then(res => {
              this.setData({
                totalCount: res.total,
                hasMore: this.data.articleList.length < res.total
              });
            });
        }
      })
      .catch(err => {
        console.error('获取文章列表失败:', err);
        this.setData({
          loading: false,
          loadingMore: false,
          showError: true,
          errorMsg: '获取文章列表失败，请稍后重试'
        });
      });
  },

  /**
   * 加载更多
   */
  loadMoreArticles: function () {
    this.loadArticles();
  },

  /**
   * 重试获取文章列表
   */
  handleRetry: function () {
    this.setData({
      articleList: [],
      page: 0,
      hasMore: true,
      showError: false
    });
    this.loadArticles();
  },

  /**
   * 跳转到文章详情
   */
  navigateToDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    if (id) {
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeDetail?id=${id}&category=${this.data.category}`
      });
    }
  },

  /**
   * 添加文章
   */
  navigateToEdit: function (e) {
    // 检查是否有ID
    const id = e.currentTarget.dataset ? e.currentTarget.dataset.id : '';
    
    if (id) {
      // 编辑现有文章
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeEdit?id=${id}&category=${this.data.category}`
      });
    } else {
      // 新增文章
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeEdit?category=${this.data.category}`
      });
    }
  },

  /**
   * 删除文章
   */
  deleteArticle: function (e) {
    const id = e.currentTarget.dataset.id;
    if (!id) return;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这篇文章吗？此操作无法撤销。',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          });
          
          db.collection('knowledge').doc(id).remove().then(() => {
            wx.hideLoading();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 从列表中移除
            const newList = this.data.articleList.filter(item => item._id !== id);
            this.setData({
              articleList: newList,
              totalCount: this.data.totalCount - 1
            });
          }).catch(err => {
            console.error('删除文章失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          });
        }
      }
    });
    return false;
  },

  // 阻止事件冒泡
  stopPropagation: function (e) {
    return false;
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.setData({
      articleList: [],
      page: 0,
      hasMore: true,
      loading: true,
      showError: false
    });
    
    this.loadArticles();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMoreArticles();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '经济支持服务 - 帮助农户获取资金支持',
      path: '/pages/economic/economicIndex'
    }
  },

  formatTime: function (timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }
}); 