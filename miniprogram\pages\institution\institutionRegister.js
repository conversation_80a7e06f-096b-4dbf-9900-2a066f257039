Page({
  data: {
    formData: {
      name: '', // 机构名称
      image: '', // 机构图片
      address: '', // 详细地址
      phone: '', // 联系电话
      serviceTarget: '', // 服务对象
      serviceItems: '', // 服务项目
      description: '' // 机构介绍
    },
    region: ['', '', ''], // 省市区
    imageUrl: '', // 上传后的图片链接
    uploading: false // 是否正在上传图片
  },

  onLoad: function (options) {
    // 检查是否有管理员权限
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    if (userRole !== 'admin') {
      wx.showToast({
        title: '无权限访问',
        icon: 'none'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 输入框值改变
  inputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    // 更新表单数据
    this.setData({
      [`formData.${field}`]: value
    });
  },
  
  // 地区选择器改变
  regionChange: function(e) {
    this.setData({
      region: e.detail.value
    });
  },
  
  // 选择图片
  chooseImage: function() {
    if (this.data.uploading) {
      return; // 如果正在上传，防止重复点击
    }
    
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 显示临时图片
        this.setData({
          'formData.image': tempFilePath,
          uploading: true
        });
        
        // 上传图片到云存储
        this.uploadImage(tempFilePath);
      }
    });
  },
  
  // 上传图片到云存储
  uploadImage: function(filePath) {
    wx.showLoading({
      title: '上传中',
    });
    
    const timestamp = new Date().getTime();
    const cloudPath = `institution/${timestamp}_${Math.random().toString(36).substring(2, 10)}.jpg`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: res => {
        wx.hideLoading();
        
        // 获取上传后的文件ID
        const fileID = res.fileID;
        
        // 保存文件ID到表单数据
        this.setData({
          imageUrl: fileID,
          uploading: false
        });
        
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('上传图片失败:', err);
        
        // 恢复上传状态
        this.setData({
          'formData.image': '',
          uploading: false
        });
        
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 提交表单
  submitForm: function() {
    // 表单验证
    if (!this.validateForm()) {
      return;
    }
    
    wx.showLoading({
      title: '提交中',
      mask: true
    });
    
    // 整理数据
    const formData = this.data.formData;
    const institutionData = {
      name: formData.name,
      image: this.data.imageUrl || '',
      province: this.data.region[0] || '',
      city: this.data.region[1] || '',
      district: this.data.region[2] || '',
      address: formData.address,
      phone: formData.phone,
      serviceTarget: formData.serviceTarget,
      serviceItems: formData.serviceItems,
      description: formData.description,
      createTime: new Date()
    };
    
    // 提交到数据库
    const db = wx.cloud.database();
    
    db.collection('institutions').add({
      data: institutionData
    }).then(res => {
      wx.hideLoading();
      
      wx.showToast({
        title: '登记成功',
        icon: 'success'
      });
      
      // 延迟返回
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    }).catch(err => {
      wx.hideLoading();
      console.error('提交表单失败:', err);
      
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
    });
  },
  
  // 表单验证
  validateForm: function() {
    const formData = this.data.formData;
    const region = this.data.region;
    
    // 验证机构名称
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入机构名称',
        icon: 'none'
      });
      return false;
    }
    
    // 验证所在地区
    if (!region[0] || !region[1] || !region[2]) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      });
      return false;
    }
    
    // 验证详细地址
    if (!formData.address.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return false;
    }
    
    // 验证联系电话
    if (!formData.phone.trim()) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return false;
    }
    
    // 可以添加更多验证
    
    return true;
  }
}); 