 <view class="profile-container">
  <!-- 头部背景区域 -->
  <view class="header-bg"></view>
  
  <!-- 用户信息区域 -->
  <view class="user-card">
    <view class="avatar-container">
      <image class="avatar" src="{{isLogin ? userInfo.avatarUrl : '/images/passerby.png'}}" mode="aspectFill"></image>
    </view>
    
    <view class="nickname-container">
      <text class="nickname">{{isLogin ? userInfo.nickName : '未登录'}}</text>
      <!-- 性别图标 -->
      <view class="gender-icon" wx:if="{{isLogin && userInfo.gender > 0}}">
        <text class="gender-symbol {{userInfo.gender === 1 ? 'male-icon' : 'female-icon'}}">{{userInfo.gender === 1 ? '♂' : '♀'}}</text>
      </view>
    </view>
    
    <!-- 显示用户角色，仅管理员显示 -->
    <view class="user-role" wx:if="{{isLogin && userRole === 'admin'}}">
      <text class="role-badge role-admin">管理员</text>
    </view>
    
    <button wx:if="{{!isLogin}}" class="login-btn" open-type="getUserInfo" bindgetuserinfo="onGetUserInfo">微信一键登录</button>
    <button wx:else class="logout-btn" bindtap="logout">退出登录</button>
  </view>
  
  <!-- 功能列表区域 -->
  <view class="function-list">
    <!-- 新增我的信息导航项 -->
    <view class="function-item" bindtap="navigateToMyInfo">
      <view class="function-left">
        <view class="function-icon icon-myinfo">👤</view>
        <text class="function-name">我的信息</text>
      </view>
      <view class="function-right">
        <text class="function-desc">修改个人信息</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <view class="function-item" bindtap="navigateToFavorites">
      <view class="function-left">
        <view class="function-icon icon-favorites">❤️</view>
        <text class="function-name">我的收藏</text>
      </view>
      <view class="function-right">
        <text class="function-desc">查看我喜欢的内容</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <view class="function-item" bindtap="navigateToPosts">
      <view class="function-left">
        <view class="function-icon icon-posts">📝</view>
        <text class="function-name">我的发帖</text>
      </view>
      <view class="function-right">
        <text class="function-desc">管理我发布的内容</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <view class="function-item" bindtap="navigateToReminders">
      <view class="function-left">
        <view class="function-icon icon-reminders">⏰</view>
        <text class="function-name">用药提醒</text>
      </view>
      <view class="function-right">
        <text class="function-desc">设置用药时间提醒</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 管理员功能 -->
    <view class="function-item" bindtap="navigateToAdmin" wx:if="{{userRole === 'admin'}}">
      <view class="function-left">
        <view class="function-icon icon-admin">🔑</view>
        <text class="function-name">管理员功能</text>
      </view>
      <view class="function-right">
        <text class="function-desc">系统管理</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 安宁疗护机构登记功能 (仅管理员可见) -->
    <view class="function-item" bindtap="navigateToInstitutionRegister" wx:if="{{userRole === 'admin'}}">
      <view class="function-left">
        <view class="function-icon icon-institution">🏥</view>
        <text class="function-name">机构登记</text>
      </view>
      <view class="function-right">
        <text class="function-desc">安宁疗护机构登记</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>
  
  <!-- 版本信息区域 -->
  <view class="footer">
    <text class="version">版本 1.0.0</text>
    <text class="copyright">© 2024 健康助手</text>
  </view>
</view>