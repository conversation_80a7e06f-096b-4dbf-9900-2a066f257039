<view class="join-container">
  <!-- 顶部标题 -->
  <view class="header">
    <view class="title-container">
      <text class="title">{{pageTitle}}</text>
      <view class="title-decoration"></view>
    </view>
  </view>

  <!-- 内容展示区域 -->
  <view class="content-section">
    <!-- 只有管理员可以编辑 -->
    <view class="admin-actions" wx:if="{{userRole === 'admin' && !isEditing}}">
      <button class="edit-btn" bindtap="startEdit">
        <text class="edit-icon">✏️</text>
        <text>编辑内容</text>
      </button>
    </view>

    <!-- 普通内容展示 -->
    <view class="content-view" wx:if="{{!isEditing}}">
      <view class="content-text">{{contentData.text}}</view>
      
      <view class="images-container">
        <view class="image-wrapper">
          <image class="content-image" src="{{contentData.image1}}" mode="widthFix" bindlongpress="saveImageToAlbum" data-url="{{contentData.image1}}"></image>
          <text class="image-tip">长按保存图片</text>
        </view>
        
        <view class="image-wrapper">
          <image class="content-image" src="{{contentData.image2}}" mode="widthFix" bindlongpress="saveImageToAlbum" data-url="{{contentData.image2}}"></image>
          <text class="image-tip">长按保存图片</text>
        </view>
      </view>
    </view>

    <!-- 编辑模式 -->
    <view class="edit-view" wx:if="{{isEditing}}">
      <!-- 文本编辑区域 -->
      <view class="text-edit">
        <textarea class="text-area" value="{{tempContent.text}}" bindinput="onTextInput" maxlength="2000" placeholder="请输入介绍文字内容"></textarea>
      </view>

      <!-- 图片上传区域 -->
      <view class="images-edit">
        <view class="image-upload">
          <text class="upload-title">图片一：</text>
          <image class="preview-image" src="{{tempContent.image1}}" mode="widthFix"></image>
          <button class="upload-btn" bindtap="chooseImage1">更换图片</button>
        </view>
        
        <view class="image-upload">
          <text class="upload-title">图片二：</text>
          <image class="preview-image" src="{{tempContent.image2}}" mode="widthFix"></image>
          <button class="upload-btn" bindtap="chooseImage2">更换图片</button>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="cancel-btn" bindtap="cancelEdit">取消</button>
        <button class="save-btn" bindtap="saveEdit">保存</button>
      </view>
    </view>
  </view>
</view> 