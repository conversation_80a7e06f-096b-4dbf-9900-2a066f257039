const app = getApp();

// 判断是否为直辖市
function isDirectlyCity(province) {
  return ['北京', '天津', '上海', '重庆', '香港', '澳门'].includes(province);
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'knowledge', // 当前活动的选项卡
    pageTitle: '生理板块',
    pageDescription: '生理症状管理与生活质量提升',
    userRole: 'user', // 默认为普通用户
    articleList: [], // 文章列表
    isLoading: false, // 是否正在加载数据
    hasMore: true, // 是否有更多数据
    pageSize: 10, // 每页获取的文章数量
    skip: 0, // 分页起始位置
    category: 'physical', // 文章类别标识

    // 安宁疗护机构相关数据
    searchKeyword: '', // 搜索关键词
    allProvinces: [
      '北京', '天津', '河北', '山西', '内蒙古', 
      '辽宁', '吉林', '黑龙江', '上海', '江苏', 
      '浙江', '安徽', '福建', '江西', '山东', 
      '河南', '湖北', '湖南', '广东', '广西', 
      '海南', '重庆', '四川', '贵州', '云南', 
      '西藏', '陕西', '甘肃', '青海', '宁夏', 
      '新疆', '台湾', '香港', '澳门'
    ], // 全国所有省份列表
    selectedProvince: '北京', // 已选择的省份
    availableCities: [], // 当前省份下有机构数据的城市/区列表
    selectedCity: '', // 已选择的城市/区
    institutions: [], // 所有机构数据
    filteredInstitutions: [] // 筛选后的机构数据
  },

  // 将全局函数引入到实例中
  isDirectlyCity: isDirectlyCity,

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    this.setData({
      userRole: userRole
    });
    
    // 只加载文章列表，不加载机构列表
    this.loadArticles();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次进入页面时重新获取文章列表
    this.setData({
      articleList: [],
      skip: 0,
      hasMore: true
    });
    this.loadArticles();
    
    // 只有当前已经在机构标签页时，才重新加载机构数据
    if (this.data.activeTab === 'institution') {
      this.loadInstitutions();
    }
  },

  // 切换Tab
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.activeTab) return;
    
    this.setData({
      activeTab: tab
    });
    
    // 如果切换到知识tab并且文章列表为空，则加载文章
    if (tab === 'knowledge' && this.data.articleList.length === 0) {
      this.loadArticles();
    }
    
    // 如果切换到机构tab并且机构列表为空，则加载机构
    if (tab === 'institution' && this.data.institutions.length === 0) {
      this.loadInstitutions();
    }
  },

  // 加载文章列表
  loadArticles: function () {
    if (this.data.isLoading || !this.data.hasMore) return;
    
    this.setData({
      isLoading: true
    });
    
    const db = wx.cloud.database();
    
    // 查询文章列表，按创建时间降序排列，并且只显示physical类别的文章
    db.collection('knowledge')
      .where({
        category: this.data.category // 只查询physical类别的文章
      })
      .orderBy('createTime', 'desc')
      .skip(this.data.skip)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        // 格式化时间
        const articles = res.data.map(item => {
          // 格式化时间为：YYYY-MM-DD
          let date = new Date(item.createTime);
          item.createTime = date.getFullYear() + '-' + 
                          ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
                          ('0' + date.getDate()).slice(-2);
          
          // 确保有摘要
          if (!item.summary) {
            // 截取内容前100个字符作为摘要
            item.summary = item.content ? item.content.replace(/<[^>]+>/g, '').substring(0, 100) : '暂无摘要';
          }
          
          return item;
        });
        
        // 更新数据
        this.setData({
          articleList: [...this.data.articleList, ...articles],
          isLoading: false,
          skip: this.data.skip + articles.length,
          hasMore: articles.length === this.data.pageSize
        });
      })
      .catch(err => {
        console.error('获取文章列表失败:', err);
        this.setData({
          isLoading: false
        });
        
        wx.showToast({
          title: '获取文章失败',
          icon: 'none'
        });
      });
  },

  // 加载机构列表
  loadInstitutions: function() {
    wx.showLoading({
      title: '加载机构信息',
    });
    
    const db = wx.cloud.database();
    
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      wx.hideLoading();
      return;
    }
    
    // 检查云环境初始化
    if (!wx.cloud.database()) {
      wx.cloud.init({
        env: 'cloud1-0g5blyk0c3ace37b', // 替换为你的云环境ID
        traceUser: true,
      });
    }
    
    // 查询所有机构
    db.collection('institutions').get().then(res => {
      wx.hideLoading();
      
      // 如果没有机构数据，创建一些测试数据
      if (res.data.length === 0) {
        this.createSampleInstitutions().then(() => {
          // 重新加载机构
          this.loadInstitutions();
        });
        return;
      }
      
      this.setData({
        institutions: res.data,
      });
      
      // 提取有机构的城市列表
      this.extractAvailableCities(this.data.selectedProvince);
      
      // 根据当前选择的省份和城市筛选机构
      this.filterInstitutions();
      
    }).catch(err => {
      wx.hideLoading();
      console.error('获取机构列表失败:', err);
      
      // 如果是集合不存在的错误，尝试创建集合和样例数据
      if (err.errCode === -502002) {
        this.createSampleInstitutions().then(() => {
          // 重新加载机构
          this.loadInstitutions();
        });
        return;
      }
      
      wx.showToast({
        title: '正在准备机构数据',
        icon: 'none',
        duration: 2000
      });
      
      // 5秒后重试
      setTimeout(() => {
        this.loadInstitutions();
      }, 5000);
    });
  },
  
  // 创建样例机构数据
  createSampleInstitutions: function() {
    const db = wx.cloud.database();
    
    // 上传默认图片到云存储
    const uploadDefaultImage = () => {
      // 先检查是否已经上传过默认图片
      return wx.cloud.database().collection('system_config').where({
        key: 'defaultHospitalImage'
      }).get().then(res => {
        if (res.data && res.data.length > 0) {
          return res.data[0].value; // 已有默认图片
        }
        
        // 上传本地图片到云端
        return wx.cloud.uploadFile({
          cloudPath: 'system/hospital_default.png',
          filePath: '/images/hospital.png', // 本地图片路径
        }).then(res => {
          const fileID = res.fileID;
          
          // 保存文件ID到数据库
          return wx.cloud.database().collection('system_config').add({
            data: {
              key: 'defaultHospitalImage',
              value: fileID,
              createTime: new Date()
            }
          }).then(() => {
            return fileID;
          });
        }).catch(() => {
          // 如果上传失败，使用默认路径
          return '/images/hospital.png';
        });
      });
    };
    
    // 先上传默认图片
    return uploadDefaultImage().then(defaultImageUrl => {
      // 样例数据
      const sampleInstitutions = [
        {
          name: '北京安宁疗护中心',
          province: '北京',
          city: '',
          district: '海淀区',
          address: '中关村南大街5号',
          phone: '010-12345678',
          serviceTarget: '晚期患者',
          serviceItems: '疼痛管理、心理支持、家庭照护培训',
          image: defaultImageUrl
        },
        {
          name: '上海仁爱医院安宁病房',
          province: '上海',
          city: '',
          district: '徐汇区',
          address: '华山路100号',
          phone: '021-87654321',
          serviceTarget: '老年患者',
          serviceItems: '舒适护理、心理支持、生活质量提升',
          image: defaultImageUrl
        },
        {
          name: '广州市第一人民医院安宁疗护科',
          province: '广东',
          city: '广州',
          district: '越秀区',
          address: '中山二路1号',
          phone: '020-66778899',
          serviceTarget: '肿瘤晚期患者',
          serviceItems: '症状控制、心理舒适、临终关怀',
          image: defaultImageUrl
        }
      ];
      
      // 添加样例数据
      const promises = sampleInstitutions.map(item => {
        return db.collection('institutions').add({
          data: item
        });
      });
      
      return Promise.all(promises).then(() => {
        console.log('样例机构数据创建成功');
      });
    }).catch(err => {
      console.error('创建样例机构数据失败:', err);
    });
  },

  // 提取指定省份下有机构的城市/区列表
  extractAvailableCities: function(province) {
    // 筛选指定省份的机构
    const provinceInstitutions = this.data.institutions.filter(item => {
      // 处理机构记录中的省份名称可能带有"市"、"省"、"自治区"等后缀的情况
      return item.province === province || 
             item.province === province + '市' || 
             item.province === province + '省' || 
             item.province === province + '自治区' || 
             item.province === province + '特别行政区';
    });
    
    // 提取城市/区列表并去重
    // 对于直辖市，提取区县；对于省，提取城市
    let locationField = this.isDirectlyCity(province) ? 'district' : 'city';
    
    const locations = [...new Set(provinceInstitutions.map(item => item[locationField]))].filter(Boolean);
    
    this.setData({
      availableCities: locations,
      selectedCity: locations.length > 0 ? locations[0] : '',
      filteredInstitutions: locations.length > 0 ? 
        provinceInstitutions.filter(item => item[locationField] === locations[0]) : 
        provinceInstitutions
    });
  },

  // 根据选择的省份和城市/区筛选机构
  filterInstitutions: function() {
    // 先获取是直辖市还是省份
    const isDirectlyCity = this.isDirectlyCity(this.data.selectedProvince);
    // 对应的字段名
    const locationField = isDirectlyCity ? 'district' : 'city';
    
    // 筛选省份（考虑可能有带后缀和不带后缀两种情况）
    let filtered = this.data.institutions.filter(item => 
      item.province === this.data.selectedProvince || 
      item.province === this.data.selectedProvince + '市' || 
      item.province === this.data.selectedProvince + '省' || 
      item.province === this.data.selectedProvince + '自治区' ||
      item.province === this.data.selectedProvince + '特别行政区'
    );
    
    // 如果选择了城市/区县，进一步筛选
    if (this.data.selectedCity) {
      filtered = filtered.filter(item => item[locationField] === this.data.selectedCity);
    }
    
    // 根据关键词搜索筛选
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = this.data.institutions.filter(item => 
        (item.name && item.name.toLowerCase().includes(keyword)) || 
        (item.address && item.address.toLowerCase().includes(keyword)) ||
        (item.serviceTarget && item.serviceTarget.toLowerCase().includes(keyword)) ||
        (item.serviceItems && item.serviceItems.toLowerCase().includes(keyword)) ||
        (item.province && item.province.includes(keyword)) ||
        (item.city && item.city.includes(keyword)) ||
        (item.district && item.district.includes(keyword))
      );
    }
    
    this.setData({
      filteredInstitutions: filtered
    });
  },

  // 搜索输入事件
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    
    // 根据新的搜索关键词筛选机构
    this.filterInstitutions();
  },
  
  // 搜索确认事件
  onSearchConfirm: function(e) {
    const value = e.detail.value;
    this.setData({
      searchKeyword: value,
      // 如果有搜索关键词，切换到机构标签页
      activeTab: value ? 'institution' : this.data.activeTab
    });
    
    // 如果有搜索关键词但机构列表为空，加载机构数据
    if (value && this.data.institutions.length === 0) {
      this.loadInstitutions();
    } else {
      // 直接根据关键词筛选现有机构
      this.filterInstitutions();
    }
  },
  
  // 清空搜索关键词
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    
    // 重新筛选机构
    this.filterInstitutions();
  },

  // 选择省份
  selectProvince: function(e) {
    const province = e.currentTarget.dataset.province;
    
    // 设置选中的省份
    this.setData({
      selectedProvince: province,
      selectedCity: '' // 清空已选城市/区
    });
    
    // 提取该省份下有机构的城市/区列表
    this.extractAvailableCities(province);
    
    // 根据新的省份筛选机构
    this.filterInstitutions();
  },

  // 选择城市/区
  selectCity: function(e) {
    const city = e.currentTarget.dataset.city;
    
    this.setData({
      selectedCity: city
    });
    
    // 根据新的城市/区筛选机构
    this.filterInstitutions();
  },

  // 跳转到机构详情
  navigateToInstitutionDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/physical/institutionDetail?id=${id}`
    });
  },

  // 跳转到机构编辑页面（新增机构）
  navigateToInstitutionEdit: function(e) {
    // 检查是否有ID
    const id = e.currentTarget.dataset ? e.currentTarget.dataset.id : '';
    
    if (id) {
      // 编辑现有机构
      wx.navigateTo({
        url: `/pages/physical/institutionEdit?id=${id}`
      });
    } else {
      // 新增机构
      wx.navigateTo({
        url: `/pages/physical/institutionEdit`
      });
    }
  },

  // 编辑机构
  editInstitution: function(e) {
    const id = e.currentTarget.dataset.id;
    this.navigateToInstitutionEdit({
      currentTarget: {
        dataset: {
          id: id
        }
      }
    });
  },

  // 删除机构
  deleteInstitution: function(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除此机构信息吗？',
      success: res => {
        if (res.confirm) {
          const db = wx.cloud.database();
          
          wx.showLoading({
            title: '正在删除',
            mask: true
          });
          
          db.collection('institutions').doc(id).remove().then(() => {
            wx.hideLoading();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 更新机构列表
            this.setData({
              institutions: this.data.institutions.filter(item => item._id !== id),
              filteredInstitutions: this.data.filteredInstitutions.filter(item => item._id !== id)
            });
          }).catch(err => {
            wx.hideLoading();
            console.error('删除机构失败:', err);
            
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 加载更多
  loadMore: function () {
    this.loadArticles();
  },

  // 跳转到文章详情页
  navigateToDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/knowledge/knowledgeDetail?id=${id}&category=${this.data.category}`
    });
  },

  // 跳转到编辑页面（新增文章）
  navigateToEdit: function (e) {
    // 检查是否有ID
    const id = e.currentTarget.dataset ? e.currentTarget.dataset.id : '';
    
    if (id) {
      // 编辑现有文章
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeEdit?id=${id}&category=${this.data.category}`
      });
    } else {
      // 新增文章
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeEdit?category=${this.data.category}`
      });
    }
  },

  // 删除文章
  deleteArticle: function (e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除此文章吗？',
      success: res => {
        if (res.confirm) {
          const db = wx.cloud.database();
          
          wx.showLoading({
            title: '正在删除',
            mask: true
          });
          
          db.collection('knowledge').doc(id).remove().then(() => {
            wx.hideLoading();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 更新文章列表
            this.setData({
              articleList: this.data.articleList.filter(item => item._id !== id)
            });
          }).catch(err => {
            wx.hideLoading();
            console.error('删除文章失败:', err);
            
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 阻止事件冒泡
  stopPropagation: function (e) {
    return false;
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: this.data.pageTitle,
      path: '/pages/physical/physicalIndex'
    }
  }
}) 