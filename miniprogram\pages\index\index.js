// miniprogram/pages/index/index.js
Page({
    /**
     * 页面的初始数据
     */
    data: {
      // 轮播图数据
      carouselItems: [],
      // 身份模块数据
      roleModules: [],
      // 信息列表数据
      articles: [],
      // 入驻机构列表数据
      institutions: [],
      // 是否为管理员
      isAdmin: false,
      // 搜索关键词
      searchKeyword: ''
    },
  
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      // 设置身份模块数据
      this.setData({
        roleModules: [
          {
            id: 1,
            name: '家属',
            iconUrl: '/images/family.png',
            roleType: 'family'
          },
          {
            id: 2, 
            name: '社工和医护人员',
            iconUrl: '/images/doctor.png',
            roleType: 'social_worker'
          },
          {
            id: 3,
            name: '案主',
            iconUrl: '/images/client.png',
            roleType: 'client'
          },
          {
            id: 4,
            name: '路人',
            iconUrl: '/images/passerby.png',
            roleType: 'passerby'
          }
        ]
      });
      
      // 检查管理员状态
      this.checkAdminStatus();
      
      // 获取轮播图数据
      this.fetchCarouselData();
      
      // 获取最新文章
      this.fetchLatestArticles();
      
      // 获取最新入驻机构
      this.fetchLatestInstitutions();
    },
    
    /**
     * 检查用户是否有管理员权限
     */
    checkAdminStatus: function() {
      // 从缓存获取用户角色
      const userRole = wx.getStorageSync('userRole') || 'user';
      // 设置管理员状态
      this.setData({
        isAdmin: userRole === 'admin'
      });
    },
    
    /**
     * 从数据库获取轮播图数据
     */
    fetchCarouselData: function() {
      const db = wx.cloud.database();
      db.collection('carousel')
        .orderBy('order', 'asc')
        .limit(3)
        .get()
        .then(res => {
          if (res.data && res.data.length > 0) {
            // 数据库已有轮播图数据
            this.setData({
              carouselItems: res.data
            });
          } else {
            // 数据库没有数据，使用默认数据并插入数据库
            this.initDefaultCarouselData();
          }
        })
        .catch(err => {
          console.error('获取轮播图数据失败:', err);
          // 使用默认数据
          this.initDefaultCarouselData();
        });
    },
    
    /**
     * 初始化默认轮播图数据
     */
    initDefaultCarouselData: function() {
      const defaultCarouselItems = [
        {
          id: 1,
          order: 1,
          imageUrl: 'https://626c-blogcloudtest-6gxk7hk81ea6e81a-1343785135.tcb.qcloud.la/news_content_images/1.jpg?sign=c570b227a402b2be1427cf230fe8fe0c&t=1744858763'
        },
        {
          id: 2,
          order: 2,
          imageUrl: 'https://626c-blogcloudtest-6gxk7hk81ea6e81a-1343785135.tcb.qcloud.la/news_content_images/1744205171180-4695.jpg?sign=105cf0501e16b931f7a886c2346c0a0f&t=1744858796' 
        },
        {
          id: 3,
          order: 3,
          imageUrl: 'https://626c-blogcloudtest-6gxk7hk81ea6e81a-1343785135.tcb.qcloud.la/news_content_images/3.jpg?sign=18cd203fb5c780af846391ffe1a98d51&t=1744858841'
        }
      ];
      
      this.setData({
        carouselItems: defaultCarouselItems
      });
      
      // 如果是管理员，将默认数据存入数据库
      if (this.data.isAdmin) {
        const db = wx.cloud.database();
        // 批量添加默认轮播图数据到数据库
        defaultCarouselItems.forEach(item => {
          db.collection('carousel').add({
            data: item
          }).catch(err => {
            console.error('添加默认轮播图失败:', err);
          });
        });
      }
    },
    
    /**
     * 编辑轮播图
     */
    editCarousel: function(e) {
      const id = e.currentTarget.dataset.id;
      // 确保用户为管理员
      if (!this.data.isAdmin) {
        wx.showToast({
          title: '没有编辑权限',
          icon: 'none'
        });
        return;
      }
      
      // 选择图片
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        camera: 'back',
        success: res => {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          
          wx.showLoading({
            title: '上传中...',
          });
          
          // 上传图片到云存储
          const cloudPath = `carousel/banner_${id}_${new Date().getTime()}.${tempFilePath.split('.').pop()}`;
          
          wx.cloud.uploadFile({
            cloudPath: cloudPath,
            filePath: tempFilePath,
            success: uploadRes => {
              const fileID = uploadRes.fileID;
              
              // 更新数据库中的轮播图URL
              const db = wx.cloud.database();
              db.collection('carousel')
                .where({
                  id: id
                })
                .update({
                  data: {
                    imageUrl: fileID
                  }
                })
                .then(() => {
                  // 更新本地数据
                  const updatedCarouselItems = this.data.carouselItems.map(item => {
                    if (item.id === id) {
                      return { ...item, imageUrl: fileID };
                    }
                    return item;
                  });
                  
                  this.setData({
                    carouselItems: updatedCarouselItems
                  });
                  
                  wx.hideLoading();
                  wx.showToast({
                    title: '更新成功',
                  });
                })
                .catch(err => {
                  console.error('更新数据库失败:', err);
                  wx.hideLoading();
                  wx.showToast({
                    title: '更新失败',
                    icon: 'none'
                  });
                });
            },
            fail: err => {
              console.error('上传图片失败:', err);
              wx.hideLoading();
              wx.showToast({
                title: '上传失败',
                icon: 'none'
              });
            }
          });
        }
      });
    },
  
    /**
     * 加载文章列表数据
     */
    loadArticles: function () {
      const db = wx.cloud.database();
      const articlesCollection = db.collection('articles');
  
      articlesCollection
        .orderBy('publishDate', 'desc')
        .limit(3)
        .get()
        .then(res => {
          console.log('从数据库获取文章成功:', res.data);
          this.setData({
            articles: res.data.map(article => {
              if (!article.coverImage) {
                article.coverImage = 'https://placehold.co/240x180/eeeeee/999999?text=默认图片';
              }
               if (!article.brief) {
                article.brief = '暂无简介';
              }
              return article;
            })
          });
        })
        .catch(err => {
          console.error('从数据库获取文章失败:', err);
          wx.showToast({
            title: '加载信息失败',
            icon: 'none'
          });
           this.setData({
             articles: []
           });
        });
    },
  
    /**
     * 点击身份模块跳转处理函数
     */
    navigateToRole: function (event) {
      const roleType = event.currentTarget.dataset.role; // 获取点击的模块类型
      console.log('点击了身份模块:', roleType);
  
      // --- 修改开始: 更新跳转路径为 functionList ---
      wx.navigateTo({
        // 跳转到 functionList 页面，并通过 url 参数传递 roleType
        url: `/pages/functionList/functionList?role=${roleType}`, // <--- 路径已修改
        success: function(res) {
          // 页面跳转成功回调
          console.log(`成功跳转到 functionList 页面，角色: ${roleType}`);
        },
        fail: function(err) {
          // 页面跳转失败回调
          console.error('跳转到 functionList 页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      // --- 修改结束 ---
    },
  
    /**
     * 获取最新文章
     */
    fetchLatestArticles: function() {
      const db = wx.cloud.database();
      
      // 从knowledge集合获取最新的3篇文章
      db.collection('knowledge')
        .orderBy('createTime', 'desc') // 按创建时间降序排列
        .limit(3) // 只获取3篇
        .get()
        .then(res => {
          const articles = res.data.map(item => {
            // 格式化时间
            if (item.createTime) {
              let date = new Date(item.createTime);
              item.createTime = date.getFullYear() + '-' + 
                              ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
                              ('0' + date.getDate()).slice(-2);
            }
            
            // 如果没有简介，从内容中截取
            if (!item.brief) {
              item.brief = item.summary || (item.content ? item.content.replace(/<[^>]+>/g, '').substring(0, 50) + '...' : '');
            }
            
            return item;
          });
          
          this.setData({
            articles: articles
          });
        })
        .catch(err => {
          console.error('获取文章列表失败:', err);
        });
    },
  
    /**
     * 跳转到文章详情
     */
    navigateToDetail: function(e) {
      const id = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `/pages/knowledge/knowledgeDetail?id=${id}`
      });
    },
  
    /**
     * 搜索框确认事件处理函数
     */
    onSearchConfirm: function(event) {
        const keyword = event.detail.value; // 获取用户输入的搜索关键词
        console.log('用户搜索:', keyword);
      
        if (!keyword || !keyword.trim()) { // 判断非空和去除前后空格
          wx.showToast({
            title: '请输入有效搜索内容',
            icon: 'none'
          });
          return;
        }
      
        // 直接在当前页面进行搜索，而不是跳转
        this.searchContent(keyword.trim());
    },
    
    /**
     * 搜索内容
     */
    searchContent: function(keyword) {
      wx.showLoading({
        title: '正在搜索...',
      });
      
      try {
        // 同时搜索文章和机构
        Promise.all([
          this.searchArticles(keyword),
          this.searchInstitutions(keyword)
        ])
        .then(([articles, institutions]) => {
          wx.hideLoading();
          
          // 更新界面数据
          this.setData({
            articles: articles,
            institutions: institutions,
            searchKeyword: keyword // 保存搜索关键词
          });
          
          // 显示搜索结果信息
          const totalResults = articles.length + institutions.length;
          if (totalResults === 0) {
            wx.showToast({
              title: '未找到相关内容',
              icon: 'none',
              duration: 2000
            });
          } else {
            wx.showToast({
              title: `找到 ${totalResults} 条结果`,
              icon: 'none',
              duration: 2000
            });
          }
        })
        .catch(err => {
          console.error('搜索失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '搜索失败，请重试',
            icon: 'none',
            duration: 2000
          });
        });
      } catch (err) {
        console.error('搜索异常:', err);
        wx.hideLoading();
        wx.showToast({
          title: '搜索功能出错，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },
    
    /**
     * 搜索文章
     */
    searchArticles: function(keyword) {
      const db = wx.cloud.database();
      const _ = db.command;
      
      // 使用正则表达式进行模糊匹配
      const regExp = db.RegExp({
        regexp: keyword,
        options: 'i' // 不区分大小写
      });
      
      return new Promise((resolve, reject) => {
        // 搜索标题、简介和内容
        db.collection('knowledge')
          .where(_.or([
            { title: regExp },
            { brief: regExp },
            { content: regExp },
            { summary: regExp }
          ]))
          .orderBy('createTime', 'desc')
          .limit(10)
          .get()
          .then(res => {
            console.log('文章搜索结果:', res.data);
            // 格式化文章数据
            const formattedArticles = res.data.map(item => {
              // 格式化时间
              if (item.createTime) {
                let date = new Date(item.createTime);
                item.createTime = date.getFullYear() + '-' + 
                                ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
                                ('0' + date.getDate()).slice(-2);
              }
              
              // 如果没有简介，从内容中截取
              if (!item.brief) {
                item.brief = item.summary || (item.content ? item.content.replace(/<[^>]+>/g, '').substring(0, 50) + '...' : '');
              }
              
              return item;
            });
            
            resolve(formattedArticles);
          })
          .catch(err => {
            console.error('搜索文章失败:', err);
            // 出错时返回空数组，不影响整体搜索
            resolve([]);
          });
      });
    },
    
    /**
     * 搜索机构
     */
    searchInstitutions: function(keyword) {
      const db = wx.cloud.database();
      const _ = db.command;
      
      // 使用正则表达式进行模糊匹配
      const regExp = db.RegExp({
        regexp: keyword,
        options: 'i' // 不区分大小写
      });
      
      return new Promise((resolve, reject) => {
        // 搜索机构名称、地址、描述等
        db.collection('institutions')
          .where(_.or([
            { name: regExp },
            { province: regExp },
            { city: regExp },
            { district: regExp },
            { description: regExp },
            { serviceInfo: regExp }
          ]))
          .orderBy('createTime', 'desc')
          .limit(10)
          .get()
          .then(res => {
            console.log('机构搜索结果:', res.data);
            resolve(res.data);
          })
          .catch(err => {
            console.error('搜索机构失败:', err);
            // 出错时返回空数组，不影响整体搜索
            resolve([]);
          });
      });
    },
    
    /**
     * 重置搜索，恢复默认显示
     */
    resetSearch: function() {
      // 清除搜索框内容和搜索关键词
      this.setData({
        searchKeyword: ''
      });
      
      // 重新获取最新文章和机构
      this.fetchLatestArticles();
      this.fetchLatestInstitutions();
    },
  
    /**
     * 获取最新入驻机构
     */
    fetchLatestInstitutions: function() {
      const db = wx.cloud.database();
      
      // 从institutions集合获取最新的6家机构
      db.collection('institutions')
        .orderBy('createTime', 'desc') // 按创建时间降序排列，如果没有createTime字段，可以替换为其他合适的排序字段
        .limit(6) // 只获取6家
        .get()
        .then(res => {
          this.setData({
            institutions: res.data
          });
        })
        .catch(err => {
          console.error('获取机构列表失败:', err);
        });
    },
    
    /**
     * 跳转到机构详情页面
     */
    navigateToInstitutionDetail: function(e) {
      const id = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `/pages/physical/institutionDetail?id=${id}`
      });
    },
    
    /**
     * 跳转到机构入驻申请小程序
     */
    navigateToInstitutionApply: function() {
      wx.navigateToMiniProgram({
        shortLink: '#小程序://金数据/ydceLKecztNudEE',
        envVersion: 'release', // 正式版
        success(res) {
          console.log('成功打开金数据小程序');
        },
        fail(err) {
          console.error('打开金数据小程序失败', err);
          wx.showToast({
            title: '打开小程序失败',
            icon: 'none'
          });
        }
      });
    },
  
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
  
    },
  
    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
      // 检查管理员状态
      this.checkAdminStatus();
      
      // 只有没有搜索关键词时才刷新数据
      if (!this.data.searchKeyword) {
        // 刷新最新文章
        this.fetchLatestArticles();
        // 刷新最新入驻机构
        this.fetchLatestInstitutions();
      }
    },
  
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {
  
    },
  
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
  
    },
  
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
       console.log('触发下拉刷新');
       this.loadArticles();
       setTimeout(() => {
          wx.stopPullDownRefresh();
       }, 500);
    },
  
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {
      console.log('触发上拉触底');
      // wx.showToast({
      //   title: '没有更多内容了',
      //   icon: 'none'
      // });
    },
  
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {
      return {
        title: '分享我的小程序',
        path: '/pages/index/index',
      }
    }
  })