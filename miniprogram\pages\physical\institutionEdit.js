const app = getApp();

// 判断是否为直辖市
function isDirectlyCity(province) {
  return ['北京', '天津', '上海', '重庆', '香港', '澳门'].includes(province);
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: '', // 机构ID，为空表示新增
    name: '', // 机构名称
    province: '北京', // 省份
    city: '', // 城市
    district: '', // 区县
    address: '', // 详细地址
    phone: '', // 联系电话
    serviceTarget: '', // 服务对象
    serviceItems: '', // 服务内容
    image: '/images/hospital.png', // 机构图片
    
    isUploading: false, // 是否正在上传图片
    showCityField: false, // 是否显示城市选择字段
    
    // 省市区选择器相关
    allProvinces: [
      '北京', '天津', '河北', '山西', '内蒙古', 
      '辽宁', '吉林', '黑龙江', '上海', '江苏', 
      '浙江', '安徽', '福建', '江西', '山东', 
      '河南', '湖北', '湖南', '广东', '广西', 
      '海南', '重庆', '四川', '贵州', '云南', 
      '西藏', '陕西', '甘肃', '青海', '宁夏', 
      '新疆', '台湾', '香港', '澳门'
    ],
    citiesMap: {}, // 省份对应的城市列表
    districtsMap: {}, // 城市对应的区县列表
    
    cityList: [], // 当前省份的城市列表
    districtList: [], // 当前城市的区县列表
    
    // 选择器是否显示
    showCityPicker: false,
    showProvinceList: false,
    showCityList: false,
    showDistrictList: false
  },

  // 将全局函数添加到实例中
  isDirectlyCity: isDirectlyCity,

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 如果有ID参数，表示是编辑现有机构
    if (options.id) {
      this.setData({
        id: options.id
      });
      
      // 加载机构信息
      this.loadInstitutionInfo(options.id);
    } else {
      // 对于新增机构，设置初始的showCityField值
      this.setData({
        showCityField: !isDirectlyCity(this.data.province)
      });
    }
    
    // 初始化城市数据
    this.initCityData();
  },
  
  // 加载机构信息
  loadInstitutionInfo: function(id) {
    wx.showLoading({
      title: '加载机构信息',
    });
    
    const db = wx.cloud.database();
    
    db.collection('institutions').doc(id).get().then(res => {
      wx.hideLoading();
      
      const data = res.data;
      
      this.setData({
        name: data.name || '',
        province: data.province || '北京',
        city: data.city || '',
        district: data.district || '',
        address: data.address || '',
        phone: data.phone || '',
        serviceTarget: data.serviceTarget || '',
        serviceItems: data.serviceItems || '',
        image: data.image || '/images/hospital.png'
      });
      
      // 根据省份更新城市列表
      this.updateCityList(data.province);
      
      // 如果有城市，更新区县列表
      if (data.city) {
        this.updateDistrictList(data.province, data.city);
      }
      
    }).catch(err => {
      wx.hideLoading();
      console.error('获取机构信息失败:', err);
      
      wx.showToast({
        title: '获取信息失败',
        icon: 'none'
      });
    });
  },
  
  // 初始化城市数据
  initCityData: function() {
    // 这里使用简化版的城市数据，实际应用中可以使用更完整的数据
    const citiesMap = {
      '北京': [],
      '天津': [],
      '上海': [],
      '重庆': [],
      '河北': ['石家庄', '唐山', '秦皇岛', '邯郸', '邢台', '保定', '张家口', '承德', '沧州', '廊坊', '衡水'],
      '山西': ['太原', '大同', '阳泉', '长治', '晋城', '朔州', '晋中', '运城', '忻州', '临汾', '吕梁'],
      // ... 其他省份的城市数据
    };
    
    const districtsMap = {
      '北京': {
        '': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区']
      },
      '上海': {
        '': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '浦东新区', '闵行区', '宝山区', '嘉定区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区']
      },
      // ... 其他直辖市和省份的区县数据
    };
    
    this.setData({
      citiesMap: citiesMap,
      districtsMap: districtsMap
    });
    
    // 根据默认省份（北京）更新城市和区县列表
    this.updateCityList('北京');
  },
  
  // 根据省份更新城市列表
  updateCityList: function(province) {
    const isDirectCity = isDirectlyCity(province);
    
    if (isDirectCity) {
      // 直辖市没有城市选项，直接设置区县列表
      this.setData({
        cityList: [],
        city: '',
        showCityField: false
      });
      
      // 更新区县列表
      this.updateDistrictList(province, '');
    } else {
      // 普通省份，设置城市列表
      const cityList = this.data.citiesMap[province] || [];
      
      this.setData({
        cityList: cityList,
        city: cityList.length > 0 ? cityList[0] : '',
        districtList: [],
        showCityField: true
      });
      
      // 如果有城市，更新区县列表
      if (cityList.length > 0) {
        this.updateDistrictList(province, cityList[0]);
      }
    }
  },
  
  // 根据省份和城市更新区县列表
  updateDistrictList: function(province, city) {
    const districts = (this.data.districtsMap[province] && this.data.districtsMap[province][city]) || [];
    
    this.setData({
      districtList: districts,
      district: districts.length > 0 ? districts[0] : ''
    });
  },
  
  // 输入框内容变化处理
  inputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [field]: value
    });
  },
  
  // 选择省份
  selectProvince: function(e) {
    const province = e.currentTarget.dataset.value;
    
    this.setData({
      province: province,
      showProvinceList: false,
      showCityPicker: false
    });
    
    // 更新城市列表
    this.updateCityList(province);
  },
  
  // 选择城市
  selectCity: function(e) {
    const city = e.currentTarget.dataset.value;
    
    this.setData({
      city: city,
      showCityList: false,
      showCityPicker: false
    });
    
    // 更新区县列表
    this.updateDistrictList(this.data.province, city);
  },
  
  // 选择区县
  selectDistrict: function(e) {
    const district = e.currentTarget.dataset.value;
    
    this.setData({
      district: district,
      showDistrictList: false,
      showCityPicker: false
    });
  },
  
  // 显示省份选择器
  showProvinceSelector: function() {
    this.setData({
      showCityPicker: true,
      showProvinceList: true,
      showCityList: false,
      showDistrictList: false
    });
  },
  
  // 显示城市选择器
  showCitySelector: function() {
    // 如果是直辖市，则不显示城市选择器，直接显示区县选择器
    if (isDirectlyCity(this.data.province)) {
      this.showDistrictSelector();
      return;
    }
    
    this.setData({
      showCityPicker: true,
      showProvinceList: false,
      showCityList: true,
      showDistrictList: false
    });
  },
  
  // 显示区县选择器
  showDistrictSelector: function() {
    this.setData({
      showCityPicker: true,
      showProvinceList: false,
      showCityList: false,
      showDistrictList: true
    });
  },
  
  // 关闭选择器
  closeSelector: function() {
    this.setData({
      showCityPicker: false,
      showProvinceList: false,
      showCityList: false,
      showDistrictList: false
    });
  },
  
  // 选择封面图片
  chooseImage: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        this.setData({
          isUploading: true
        });
        
        // 上传图片到云存储
        this.uploadImage(tempFilePath);
      }
    });
  },
  
  // 上传图片到云存储
  uploadImage: function(filePath) {
    const cloudPath = 'institution_images/' + Date.now() + filePath.match(/\.[^.]+?$/)[0];
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: res => {
        const fileID = res.fileID;
        
        this.setData({
          image: fileID,
          isUploading: false
        });
        
        wx.showToast({
          title: '上传成功',
          icon: 'success'
        });
      },
      fail: err => {
        console.error('上传图片失败:', err);
        
        this.setData({
          isUploading: false
        });
        
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 保存机构信息
  saveInstitution: function() {
    // 表单验证
    if (!this.data.name) {
      wx.showToast({
        title: '请输入机构名称',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.address) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中',
      mask: true
    });
    
    const db = wx.cloud.database();
    
    // 准备数据
    const data = {
      name: this.data.name,
      province: this.data.province,
      city: this.data.city,
      district: this.data.district,
      address: this.data.address,
      phone: this.data.phone,
      serviceTarget: this.data.serviceTarget,
      serviceItems: this.data.serviceItems,
      image: this.data.image,
      updateTime: new Date()
    };
    
    let savePromise;
    
    if (this.data.id) {
      // 编辑现有机构
      savePromise = db.collection('institutions').doc(this.data.id).update({
        data: data
      });
    } else {
      // 新增机构
      data.createTime = new Date(); // 添加创建时间
      savePromise = db.collection('institutions').add({
        data: data
      });
    }
    
    savePromise.then(res => {
      wx.hideLoading();
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    }).catch(err => {
      wx.hideLoading();
      console.error('保存机构信息失败:', err);
      
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: this.data.id ? '编辑安宁疗护机构' : '添加安宁疗护机构',
      path: '/pages/physical/physicalIndex'
    }
  }
}) 