/* miniprogram/pages/index/index.wxss */

/* 整体页面容器样式 */
.page-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 20rpx;
    background-color: #f4f4f4;
    min-height: 100vh;
    width: 100%;
  }
  
  
  /* 搜索框区域样式 */
  .search-bar {
    display: flex;
    align-items: center;
    width: 95%; /* 宽度占父容器的95% */
    height: 70rpx; /* 搜索框高度 */
    margin-top: 20rpx;
    background-color: #ffffff;
    border-radius: 35rpx; /* 圆角 */
    padding: 0 20rpx; /* 左右内边距 */
    box-sizing: border-box; /* 让 padding 不影响总宽度 */
    margin-bottom: 20rpx; /* 与下方轮播图的间距 */
  }
  
  .search-icon {
    margin-right: 10rpx; /* 图标和输入框的间距 */
  }
  
  .search-input {
    flex: 1; /* 占据剩余空间 */
    height: 100%;
    font-size: 28rpx;
    color: #333;
  }
  
  .search-placeholder {
    color: #999; /* placeholder 文字颜色 */
    font-size: 28rpx;
  }
  
  /* 搜索取消按钮 */
  .search-cancel {
    font-size: 28rpx;
    color: #4a90e2;
    padding-left: 20rpx;
    white-space: nowrap;
  }
  
  /* 备选搜索框样式 */
  /* .search-input-alt {
    flex: 1;
    height: 100%;
    font-size: 28rpx;
    padding-right: 10rpx;
  }
  .search-button {
    background-color: #07c160;
    color: white;
    border: none;
    margin-left: 10rpx;
  } */
  
  /* 1. 轮播图样式 */
  .carousel {
    width: 95%; /* 宽度与搜索框一致 */
    height: 350rpx;
    border-radius: 16rpx; /* 给轮播图也加圆角 */
    overflow: hidden; /* 隐藏超出圆角的部分 */
    margin-bottom: 20rpx; /* 与下方模块的间距 */
  }
  
  .carousel-image {
    width: 100%;
    height: 100%;
  }
  
  /* 轮播图编辑按钮样式 */
  .carousel-edit-btn {
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    width: 70rpx;
    height: 70rpx;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
    z-index: 100;
  }
  
  .edit-icon {
    font-size: 36rpx;
    color: white;
  }
  
  /* 2. 身份模块区域样式 (2x2 布局) */
  .role-modules {
    display: flex; /* 使用 Flexbox 布局 */
    flex-wrap: wrap; /* 允许换行 */
    justify-content: space-between; /* 两端对齐，中间留空隙 */
    width: 95%;
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 20rpx; /* 内边距 */
    box-sizing: border-box;
    margin-bottom: 20rpx; /* 与下方列表的间距 */
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .role-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 计算宽度，(100% - 左右padding - 中间间隙) / 2 */
    /* 这里简化处理，假设中间间隙由 justify-content 控制，左右给点边距 */
    width: 48%; /* 每个模块大致占一半宽度，留出空隙 */
    margin-bottom: 20rpx; /* 同行及不同行之间的垂直间距 */
    cursor: pointer;
    padding: 10rpx 0; /* 给每个item一点上下padding */
  }
  /* 去掉最后两个元素的下边距，避免底部多余间距 */
  .role-item:nth-last-child(-n+2) {
     margin-bottom: 0;
  }
  
  
  .role-icon {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 10rpx;
  }
  
  .role-name {
    font-size: 26rpx;
    color: #333;
  }
  
  /* 3. 信息列表区域样式 */
  .article-list-title {
    font-size: 32rpx;
    font-weight: bold;
    margin: 40rpx 30rpx 20rpx;
    color: #6a5c4c;
    position: relative;
    padding-left: 20rpx;
    align-self: flex-start;
    width: 100%;
    box-sizing: border-box;
  }
  
  .article-list-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 6rpx;
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(to bottom, #ffb88c, #ffa372);
    border-radius: 4rpx;
  }
  
  .article-list {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
  }
  
  .article-item {
    display: flex;
    background-color: rgba(255, 255, 255, 0.85);
    border-radius: 16rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(211, 187, 164, 0.15);
    transition: all 0.3s ease;
  }
  
  .article-item:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(211, 187, 164, 0.1);
  }
  
  .article-image {
    width: 180rpx;
    height: 180rpx;
    border-radius: 10rpx;
    flex-shrink: 0;
    margin-right: 20rpx;
  }
  
  .article-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
  }
  
  .article-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #6a5c4c;
    margin-bottom: 10rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .article-brief {
    font-size: 26rpx;
    color: #9c8e7f;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 10rpx;
  }
  
  .article-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 22rpx;
    color: #b3a090;
  }
  
  .article-author {
    color: #ffb88c;
  }
  
  .no-data-tip {
    text-align: center;
    padding: 40rpx 0;
    color: #9c8e7f;
    font-size: 28rpx;
  }
  
  /* 统一后的机构入驻引导条/标题样式 */
  .institution-apply-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* background-color: #fdf6ec; */ /* 移除背景色 */
    padding: 0 20rpx 20rpx 0; /* 上0，右20，下20，左0 - 左侧交给内部元素padding */
    margin: 40rpx 30rpx 0; /* 上40，左右30，下0 - 与article-list-title的外边距对齐 */
    width: auto; /* 移除固定宽度 */
    position: relative; /* 为了 ::before 定位 */
    border-bottom: 1rpx solid #f0f0f0; /* 添加一个底部分隔线，可选 */
    padding-bottom: 20rpx; /* 增加底部空间 */
  }
  
  /* 应用类似 article-list-title 的样式 */
  .banner-text-wrapper {
    display: flex;
    align-items: center;
    position: relative; /* For ::before positioning */
    padding-left: 20rpx; /* 与 article-list-title 对齐 */
  }
  
  /* 添加左侧竖线 */
  .banner-text-wrapper::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(to bottom, #ffb88c, #ffa372); /* 保持与资讯标题一致 */
    border-radius: 4rpx;
  }
  
  .banner-text {
    display: flex;
    flex-direction: column;
    margin-left: 0; /* Use wrapper padding */
  }
  
  .banner-title {
    font-size: 32rpx; /* 与 article-list-title 一致 */
    font-weight: bold; /* 与 article-list-title 一致 */
    color: #6a5c4c; /* 与 article-list-title 一致 */
    margin-bottom: 0; /* 移除间距 */
  }
  
  .banner-subtitle {
    /* 隐藏副标题 */
    display: none;
  }
  
  .banner-link {
    font-size: 26rpx;
    color: #4a90e2; /* 使用之前按钮的蓝色 */
    font-weight: 500;
  }
  
  .institution-section {
    margin: 0;
    padding-bottom: 20rpx;
    width: 100%;
    box-sizing: border-box;
    align-self: center;
    padding: 0 30rpx;
  }
  
  .institution-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
    padding: 20rpx 0;
  }
  
  /* 恢复并美化机构列表项样式 */
  .institution-item {
    background-color: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
  }
  
  /* 点击效果 */
  .institution-item:active {
    transform: scale(0.97);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  }
  
  .institution-image {
    width: 100%;
    height: 180rpx;
    object-fit: cover;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
  }
  
  /* 机构信息样式 */
  .institution-info {
    padding: 16rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  /* 机构名称样式 */
  .institution-name {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 8rpx;
  }
  
  .institution-address {
    font-size: 24rpx;
    color: #888;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    margin-top: auto;
  }
  
  /* 移除不再使用的 -nobg 类样式 */
  .institution-item-nobg,
  .institution-info-nobg,
  .institution-name-nobg {
    /* 可以留空，或者设置 display: none 以确保它们不影响 */
    /* display: none; */ 
  }
  
  .no-data-tip {
    text-align: center;
    padding: 40rpx 0;
    color: #999;
    font-size: 28rpx;
    grid-column: span 2; /* 确保提示占满两列 */
  }
  