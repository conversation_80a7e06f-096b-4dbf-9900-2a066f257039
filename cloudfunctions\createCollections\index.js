// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境

// 云函数入口函数
exports.main = async (event, context) => {
  const db = cloud.database()
  
  try {
    // 尝试创建集合
    const collections = [
      'knowledge',  // 知识文章集合
      'institution', // 机构信息集合
      'user',  // 用户信息集合
      'volunteerJoinUs' // 志愿者和互助小组招募信息集合，通过type字段区分类型
    ]
    
    // 获取所有集合
    const existingCollections = await db.listCollections().then(res => res.data.map(item => item.name))
    
    // 创建不存在的集合
    const createPromises = collections
      .filter(name => !existingCollections.includes(name))
      .map(name => db.createCollection(name))
    
    if (createPromises.length > 0) {
      await Promise.all(createPromises)
    }
    
    return {
      success: true,
      message: '集合创建成功',
      created: collections.filter(name => !existingCollections.includes(name))
    }
  } catch (error) {
    console.error(error)
    return {
      success: false,
      message: '创建集合失败',
      error
    }
  }
} 