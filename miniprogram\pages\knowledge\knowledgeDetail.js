const app = getApp();

Page({
  data: {
    article: null, // 文章数据
    isLoading: true, // 是否正在加载
    userRole: 'user', // 用户角色
    cloudEnv: '', // 云环境ID
    category: 'knowledge' // 文章分类，默认为knowledge
  },

  onLoad: function (options) {
    // 获取用户角色
    const userRole = wx.getStorageSync('userRole') || 'user';
    
    // 获取云环境ID
    const cloudEnv = wx.cloud.DYNAMIC_CURRENT_ENV || 'blogcloudtest-6gxk7hk81ea6e81a'; 
    
    // 获取分类（如果有）
    const category = options.category || 'knowledge';
    
    this.setData({
      userRole: userRole,
      cloudEnv: cloudEnv,
      category: category
    });
    
    if (options.id) {
      this.fetchArticle(options.id);
    } else {
      this.setData({
        isLoading: false
      });
      
      wx.showToast({
        title: '文章不存在',
        icon: 'none'
      });
    }
  },

  // 获取文章详情
  fetchArticle: function (id) {
    const db = wx.cloud.database();
    
    this.setData({
      isLoading: true
    });
    
    db.collection('knowledge').doc(id).get().then(res => {
      // 格式化日期
      if (res.data.createTime) {
        let date = new Date(res.data.createTime);
        res.data.createTime = date.getFullYear() + '-' + 
                            ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
                            ('0' + date.getDate()).slice(-2);
      }
      
      // 处理内容中的云存储图片路径，确保图片可以正确显示
      if (res.data.content) {
        // 使用正则表达式替换云存储图片路径中的特殊字符
        res.data.content = res.data.content.replace(/src="cloud:\/\/([^"]+)"/g, function(match, p1) {
          // 确保cloud://后的路径可以被正确解析
          return `src="cloud://${p1}"`;
        });
      }
      
      this.setData({
        article: res.data,
        isLoading: false
      });
      
      // 设置页面标题
      if (res.data.title) {
        wx.setNavigationBarTitle({
          title: res.data.title
        });
      }
    }).catch(err => {
      console.error('获取文章详情失败:', err);
      
      this.setData({
        isLoading: false
      });
      
      wx.showToast({
        title: '获取文章失败',
        icon: 'none'
      });
    });
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack();
  },

  // 返回列表页
  navigateToList: function () {
    wx.navigateBack({
      delta: 1
    });
  },

  // 跳转到编辑页面
  navigateToEdit: function () {
    wx.navigateTo({
      url: `/pages/knowledge/knowledgeEdit?id=${this.data.article._id}&category=${this.data.category}`
    });
  },

  // 删除文章
  deleteArticle: function () {
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除此文章吗？',
      success: res => {
        if (res.confirm) {
          const db = wx.cloud.database();
          
          wx.showLoading({
            title: '正在删除',
            mask: true
          });
          
          db.collection('knowledge').doc(this.data.article._id).remove().then(() => {
            wx.hideLoading();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
            // 延迟返回，让用户看到成功提示
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }).catch(err => {
            wx.hideLoading();
            console.error('删除文章失败:', err);
            
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  }
}); 